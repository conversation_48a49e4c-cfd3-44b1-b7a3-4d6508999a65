import { ApiResponse } from "~/types/api/Api";
import { rNETOrder } from "~/types/api/businessConsoleService/rNETOrder";
import { API_BASE_URL, apiRequest } from "~/utils/api";

export async function getrNETOrders(
  request?: Request
): Promise<ApiResponse<{ orders: rNETOrder[] }>> {

  const response = await apiRequest<{ orders: rNETOrder[] }>(

    `${API_BASE_URL}/bc/mnetadmin/orders`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Response("Failed to fetch orders");
  }
}