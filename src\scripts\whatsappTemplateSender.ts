import ExcelJS from 'exceljs';
// import { getFirebaseAdmin } from '../../apps/business-console/app/services/firebase.server.ts';
import { TemplateMessageController } from '../controllers/templateMessageController.js';
import { SendTemplateRequest, MetaWhatsAppResponse, WhatsAppConnectionData, TemplateCampaignRequest } from '../types/whatsapp.js';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import dotenv from 'dotenv';
import { templateService } from '../services/templateService.js';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// firebase.server.ts
import { initializeApp, cert, getApps } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { CampaignType, CustomerSegment, MessageCategory } from '@/database/entities/NotificationLog.js';

export function getFirebaseAdmin() {
    if (getApps().length === 0) {
        const privateKey = process.env.FIREBASE_PRIVATE_KEY?.replace(/@/g, '\n');

        initializeApp({
            credential: cert({
                projectId: process.env.FIREBASE_PROJECT_ID,
                clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
                privateKey: privateKey,
            }),
        });
    }
    return getFirestore();
}

interface InputRow {
    sellerId: string;
    templateId: string;
    targetPhoneNumber: string;
    _rowNumber?: number; // Store row number for updates
    [key: string]: string | number | undefined; // Allow dynamic variables
}

interface OutputRow extends InputRow {
    messageId?: string;
    error?: string;
    timestamp: string;
    status: 'SUCCESS' | 'FAILED';  // Add status field
}

interface TemplateResponse {
    success: boolean;
    messageId?: string;
    error?: string;
}

class WhatsAppTemplateSender {
    private templateMessageController: TemplateMessageController;
    private db: any;

    constructor() {
        this.templateMessageController = new TemplateMessageController();
        this.db = getFirebaseAdmin();
    }

    private async getSellerData(sellerId: string) {
        try {
            const doc = await this.db.collection("facebook-connects")
                .doc(sellerId)
                .get();

            if (!doc.exists) {
                throw new Error(`WhatsApp connection not found for seller ${sellerId}`);
            }

            const connectionData = doc.data() as WhatsAppConnectionData;
            if (!connectionData.wabaId) {
                throw new Error(`WhatsApp Business Account ID not found for seller ${sellerId}`);
            }

            return {
                wabPhoneNumberId: connectionData.mNetConnectedPhoneNumberId,
                accessToken: connectionData.access.access_token
            };
        } catch (error) {
            console.error(`Error fetching seller data for ${sellerId}:`, error);
            throw error;
        }
    }

    private prepareTemplateVariables(row: InputRow) {
        const template = templateService.getAvailableTemplates().find(t => t.templateId === row.templateId);
        if (!template) {
            throw new Error(`Template ${row.templateId} not found`);
        }

        const variables: { [key: string]: any } = {
            body: {},
            header: {},
            footer: {},
            button: {}
        };

        // Process body variables
        if (template.bodyVariables) {
            template.bodyVariables.forEach(varName => {
                if (row[varName] !== undefined) {
                    variables.body[varName] = row[varName];
                }
            });
        }

        // Process header variables
        if (template.headerVariables) {
            template.headerVariables.forEach(varName => {
                if (row[varName] !== undefined) {
                    variables.header[varName] = row[varName];
                }
            });
        }

        // Process footer variables
        if (template.footerVariables) {
            template.footerVariables.forEach(varName => {
                if (row[varName] !== undefined) {
                    variables.footer[varName] = row[varName];
                }
            });
        }
        
        // Process CTA/button variables
        if (template.ctas && template.ctas.length > 0) {
            template.ctas.forEach(cta => {
                // For URL type CTAs, map the button variable to the URL
                if (cta.type === 'url' && row[cta.value] !== undefined) {
                    variables.button[cta.value] = row[cta.value];
                }
                // For quick_reply type CTAs, the value is usually static
                // but can be overridden if provided in the row data
                else if (cta.type === 'quick_reply' && row[cta.value] !== undefined) {
                    variables.button[cta.value] = row[cta.value];
                }
            });
        }

        return variables;
    }

    private async sendTemplateMessage(row: InputRow): Promise<TemplateResponse> {
        try {
            const sellerData = await this.getSellerData(row.sellerId);
            const templateVariables = this.prepareTemplateVariables(row);

            const request: TemplateCampaignRequest = {
                templateId: row.templateId,
                targetPhoneNumber: row.targetPhoneNumber,
                wabPhoneNumberId: sellerData.wabPhoneNumberId,
                accessToken: sellerData.accessToken,
                variables: templateVariables,
                campaignId: (row.campaignId as string) || '8073029404-18-06-2025-1940',
                campaignName: (row.campaignName as string) || row.templateId + "-" + "18-06-2025-1940",
                campaignType: (row.campaignType as CampaignType) || CampaignType.MARKETING,
                messageCategory: (row.messageCategory as MessageCategory) || MessageCategory.DIRECT_ORDER_PROMOTION,
                customerSegment: (row.customerSegment as CustomerSegment) || CustomerSegment.NEW_CUSTOMER,
                tags: row.tags ? (row.tags as string).split(',').map((tag: string) => tag.trim()) : ["mg-biryani-marketing", "direct-order-promotion", "2nd-time-promotion"]
            };

            // Create mock request and response objects
            const mockReq = {
                body: request
            };

            const mockRes = {
                json: (data: any) => data
            };

            const response = await this.templateMessageController.sendTemplateMessage(mockReq as any, mockRes as any);
            const result = response as unknown as MetaWhatsAppResponse;
            console.log(JSON.stringify(result, null, 2));
            
            return {
                success: true,
                messageId: result.details?.messages[0]?.id || undefined,
                error: undefined
            };
        } catch (error: any) {
            return {
                success: false,
                messageId: undefined,
                error: error.message || 'Unknown error occurred'
            };
        }
    }

    private async readExcelFile(filePath: string, retryOnly: boolean = false): Promise<{ rows: InputRow[], workbook: ExcelJS.Workbook, worksheet: ExcelJS.Worksheet }> {
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(filePath);
        
        const worksheet = workbook.getWorksheet(1);
        if (!worksheet) {
            throw new Error('No worksheet found in the Excel file');
        }

        const rows: InputRow[] = [];
        const headers: string[] = [];

        // Get headers from first row
        worksheet.getRow(1).eachCell((cell, colNumber) => {
            headers[colNumber - 1] = cell.value?.toString() || '';
        });

        // Check if result columns already exist
        const hasStatusColumn = headers.includes('status');
        const hasMessageIdColumn = headers.includes('messageId');
        const hasErrorColumn = headers.includes('error');
        const hasTimestampColumn = headers.includes('timestamp');

        // Add result columns if they don't exist
        if (!hasStatusColumn) {
            headers.push('status');
            worksheet.getCell(1, headers.length).value = 'status';
        }
        if (!hasMessageIdColumn) {
            headers.push('messageId');
            worksheet.getCell(1, headers.length).value = 'messageId';
        }
        if (!hasErrorColumn) {
            headers.push('error');
            worksheet.getCell(1, headers.length).value = 'error';
        }
        if (!hasTimestampColumn) {
            headers.push('timestamp');
            worksheet.getCell(1, headers.length).value = 'timestamp';
        }

        // Style the header row
        worksheet.getRow(1).font = { bold: true };
        worksheet.getRow(1).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE0E0E0' }
        };

        // Read data rows
        worksheet.eachRow((row, rowNumber) => {
            if (rowNumber === 1) return; // Skip header row

            const rowData: any = { _rowNumber: rowNumber }; // Store row number for updates
            row.eachCell((cell, colNumber) => {
                const header = headers[colNumber - 1];
                if (header) {
                    rowData[header] = cell.value?.toString() || '';
                }
            });

            // If retryOnly is true, only include rows that failed or have no status
            if (retryOnly) {
                const status = rowData.status;
                if (!status || status === 'FAILED' || status === '') {
                    rows.push(rowData as InputRow);
                }
            } else {
                // If not retry mode, include all rows that don't have a SUCCESS status
                const status = rowData.status;
                if (!status || status !== 'SUCCESS') {
                    rows.push(rowData as InputRow);
                }
            }
        });

        return { rows, workbook, worksheet };
    }

    private async updateExcelFile(
        filePath: string, 
        workbook: ExcelJS.Workbook, 
        worksheet: ExcelJS.Worksheet, 
        results: Map<number, OutputRow>
    ) {
        // Update rows with results
        results.forEach((result, rowNumber) => {
            const row = worksheet.getRow(rowNumber);
            
            // Find column indices for result fields
            const headers: string[] = [];
            worksheet.getRow(1).eachCell((cell, colNumber) => {
                headers[colNumber - 1] = cell.value?.toString() || '';
            });

            const statusColIndex = headers.indexOf('status') + 1;
            const messageIdColIndex = headers.indexOf('messageId') + 1;
            const errorColIndex = headers.indexOf('error') + 1;
            const timestampColIndex = headers.indexOf('timestamp') + 1;

            // Update cells
            if (statusColIndex > 0) {
                const statusCell = row.getCell(statusColIndex);
                statusCell.value = result.status;
                
                // Apply color formatting based on status
                if (result.status === 'SUCCESS') {
                    statusCell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FF90EE90' }  // Light green
                    };
                } else {
                    statusCell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FFFFB6C1' }  // Light red
                    };
                }
            }

            if (messageIdColIndex > 0) {
                row.getCell(messageIdColIndex).value = result.messageId || '';
            }

            if (errorColIndex > 0) {
                row.getCell(errorColIndex).value = result.error || '';
            }

            if (timestampColIndex > 0) {
                row.getCell(timestampColIndex).value = result.timestamp;
            }
        });

        // Create directory if it doesn't exist
        const outputDir = path.dirname(filePath);
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        await workbook.xlsx.writeFile(filePath);
    }

    public async processExcelFile(inputFilePath: string, retryOnly: boolean = false) {
        try {
            // Read input Excel file
            console.log('Reading input file...');
            const { rows, workbook, worksheet } = await this.readExcelFile(inputFilePath, retryOnly);

            if (rows.length === 0) {
                console.log('No rows to process. All messages have already been sent successfully or no failed messages found for retry.');
                return;
            }

            // Process each row
            console.log(`Processing ${rows.length} rows...`);
            const results = new Map<number, OutputRow>();
            
            for (const row of rows) {
                console.log(`Processing row for seller ${row.sellerId}...`);
                
                const result = await this.sendTemplateMessage(row);
                
                const outputRow: OutputRow = {
                    ...row,
                    messageId: result.messageId,
                    error: result.error,
                    timestamp: new Date().toISOString(),
                    status: result.success ? 'SUCCESS' : 'FAILED'
                };

                // Store result with row number for updating
                if (row._rowNumber) {
                    results.set(row._rowNumber, outputRow);
                }
            }

            // Update the input Excel file with results
            console.log('Updating input file with results...');
            await this.updateExcelFile(inputFilePath, workbook, worksheet, results);

            console.log(`Processing complete. Results updated in ${inputFilePath}`);
        } catch (error) {
            console.error('Error processing Excel file:', error);
            throw error;
        }
    }
}

// Example usage
const sender = new WhatsAppTemplateSender();
const inputFile = path.join(process.cwd(), 'src', 'scripts', 'input', 'mg-wa-marketing.xlsx');

// Process all rows (first run)
sender.processExcelFile(inputFile, false)
    .then(() => console.log('Script completed successfully'))
    .catch(error => console.error('Script failed:', error));

// Uncomment below to retry only failed messages
// sender.processExcelFile(inputFile, true)
//     .then(() => console.log('Retry completed successfully'))
//     .catch(error => console.error('Retry failed:', error)); 