import { ActionFunction, json, LoaderFunction } from "@remix-run/node";
import { useLoaderData, useNavigate, useOutletContext } from "@remix-run/react";
import { Menu, MoveLeft } from "lucide-react";
import ActiveBanners from "~/components/common/ActiveBanners";
import InActiveBanners from "~/components/common/InActiveBanners";
import { Button } from "~/components/ui/button";
import { getBuyerNBanners, updateBuyerNBanners, updateBuyerNSequence } from "~/services/buyerSetting";
import { BuyerNetworkBanners } from "~/types/api/businessConsoleService/BuyerAccountingResponse";
import { withAuth, withResponse } from "~/utils/auth-utils";

export const loader: LoaderFunction = withAuth(async ({ request }) => {
      try {
            const response = await getBuyerNBanners(request);
            return withResponse({ banners: response.data }, response.headers);
      } catch (error) {
            throw new Response("Failed to fetch banners", { status: 500 });
      }
});

export const action: ActionFunction = async ({ request }) => {
      const formData = await request.formData();
      const bannerId = Number(formData.get("bannerId"));
      const netWorkId = Number(formData.get("netWorkId"));
      const sequenceId = Number(formData.get("sequenceId"));
      const actionType = formData.get("actionType");

      if (actionType === "updateBannerStatus") {
            try {
                  if (!bannerId) return json({ errorMessage: "BannerId must be a valid number" });
                  const response = await updateBuyerNBanners(bannerId, request);
                  return withResponse({
                        banners: response.data,
                  }, response.headers);
            } catch (error) {
                  throw new Response("Failed to update banner status", { status: 500 });
            }
      }

      if (actionType === "updateSequence") {
            try {
                  if (!bannerId) return json({ errorMessage: "BannerId must be a valid number" });
                  if (!sequenceId) return json({ errorMessage: "SequenceId must be a valid number" });
                  if (!netWorkId) return json({ errorMessage: "NetworkId must be a valid number" });
                  const payload = { id: bannerId, sequenceId: sequenceId, networkId: netWorkId };
                  const response = await updateBuyerNSequence(payload, request);
                  return withResponse({ message: "Banner sequence updated successfully" }, response.headers);
            } catch (error) {
                  throw new Response("Failed to update banner sequence", { status: 500 });
            }
      }
};

export default function Nbanners() {
      const { banners } = useLoaderData<typeof loader>();
      const inactiveBanners = banners?.filter((banner: BuyerNetworkBanners) => !banner?.active);
      const activeBanners = banners?.filter((banner: BuyerNetworkBanners) => banner?.active);
      const { setIsOpen } = useOutletContext<{ setIsOpen: (open: boolean) => void }>()

      return (
            <div className="min-h-screen w-full bg-gray-50">
                  {/* Header */}
                  <header className="flex items-center justify-between bg-white shadow-lg p-3 sticky top-0 z-10 px-4">
                        <h1 className="text-xl font-semibold  text-gray-800 text-center">My Banners</h1>

                        <Button
                              onClick={() => setIsOpen(true)}
                              variant='ghost'
                              size='icon'
                        >
                              <Menu className="h-6 w-6" />
                        </Button>

                  </header>

                  {/* Main Content */}
                  <main className="max-w-7xl mx-auto py-2 px-4 sm:px-6 lg:px-8">
                        {/* Active Banners Section */}
                        <section >
                              <h2 className="text-xl font-semibold text-gray-700 mb-2">Active Banners</h2>
                              {Array.isArray(activeBanners) && activeBanners.length > 0 ? (
                                    <div className="grid gap-2 sm:grid-cols-1  ">
                                          {activeBanners.map((banner: BuyerNetworkBanners) => (
                                                <ActiveBanners key={banner.id} bannerDetails={banner} />
                                          ))}
                                    </div>
                              ) : (
                                    <div className="flex flex-col items-center justify-center py-12 bg-white rounded-xl shadow-md border border-gray-200">
                                          <svg
                                                className="w-16 h-16 text-gray-400 mb-4"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                                xmlns="http://www.w3.org/2000/svg"
                                          >
                                                <path
                                                      strokeLinecap="round"
                                                      strokeLinejoin="round"
                                                      strokeWidth="2"
                                                      d="M3 12h18M3 6h18M3 18h18"
                                                />
                                          </svg>
                                          <p className="text-lg font-medium text-gray-600">No Active Banners</p>
                                          <p className="text-sm text-gray-500 mt-1">
                                                Add some banners to get started!
                                          </p>
                                    </div>
                              )}
                        </section>

                        {/* Inactive Banners Section */}
                        <section>
                              <h2 className="text-xl font-semibold text-gray-700 mb-2">Inactive Banners</h2>
                              {Array.isArray(inactiveBanners) && inactiveBanners.length > 0 ? (
                                    <div className="grid gap-4 sm:grid-cols-1 ">
                                          {inactiveBanners.map((banner: BuyerNetworkBanners) => (
                                                <InActiveBanners key={banner.id} bannerDetails={banner} />
                                          ))}
                                    </div>
                              ) : (
                                    <div className="flex flex-col items-center justify-center py-12 bg-white rounded-xl shadow-md border border-gray-200">
                                          <svg
                                                className="w-16 h-16 text-gray-400 mb-4"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                                xmlns="http://www.w3.org/2000/svg"
                                          >
                                                <path
                                                      strokeLinecap="round"
                                                      strokeLinejoin="round"
                                                      strokeWidth="2"
                                                      d="M3 12h18M3 6h18M3 18h18"
                                                />
                                          </svg>
                                          <p className="text-lg font-medium text-gray-600">No Inactive Banners</p>
                                          <p className="text-sm text-gray-500 mt-1">
                                                Inactive banners will appear here.
                                          </p>
                                    </div>
                              )}
                        </section>
                  </main>
            </div>
      );
}