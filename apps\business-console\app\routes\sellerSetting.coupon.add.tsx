import type { LoaderFunction } from "@remix-run/node";
import { json, use<PERSON><PERSON>cher, use<PERSON>oader<PERSON><PERSON>, useNavigate } from "@remix-run/react";
import { CalendarIcon, PlusIcon, Trash2Icon } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { Button } from "~/components/ui/button";
import { Calendar } from "~/components/ui/calendar";
import { Input } from "~/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "~/components/ui/select";
import { addCoupon, getCouponPreload } from "~/services/coupons";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { format } from "date-fns";
import { useToast } from "~/components/ui/ToastProvider";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "~/components/ui/tabs";
import {
  CouponPreloadType,
  CouponPayloadType,
  AddCouponType,
  IdConfigItem,
  ConfigKey,
  ConfigAdditionalKey,
  configKeysEnum,
  DISCOUNT_UNIT_MAP,
  CouponDetailsType,
} from "~/types/api/businessConsoleService/coupons";

export const loader: LoaderFunction = withAuth(async ({ request }) => {
  try {
    const response = await getCouponPreload(request);
    return withResponse({ data: response.data }, response.headers);
  } catch (error) {
    console.error("Error in loader:", error);
    throw new Response("Failed to fetch coupon data", {
      status: 500,
    });
  }
});

export const action = withAuth(async ({ request }) => {
  const formData = await request.formData();
  const actionType = formData.get("actionType");
  const payloadRaw = formData.get("payload");

  if (actionType === "addCoupon") {
    try {
      const parsedPayload = JSON.parse(payloadRaw as string);
      const requestPayload: AddCouponType = transformToAddCoupon(parsedPayload);
      const response = await addCoupon(requestPayload, request);
      return withResponse(
        { data: response.data, success: true },
        response.headers
      );
    } catch (error) {
      console.error("Error in action:", error);
      return json({ data: null, success: false }, { status: 500 });
    }
  }

  console.log("Invalid action type:", actionType);
  return json({ data: null, success: false }, { status: 400 });
});

function transformToAddCoupon(payload: CouponPayloadType): AddCouponType {
  const convertItems = (
    base?: IdConfigItem[],
    additional?: IdConfigItem[]
  ): { property: string; operator: string; value: string }[] => {
    const allItems = [...(base || []), ...(additional || [])];

    return allItems
      .filter((item) => item.value !== undefined && item.value !== "")
      .map((item) => ({
        property: item.property,
        operator: item.operator?.trim() || "EQ",
        value: item.value,
      }));
  };

  return {
    code: payload.couponCode || "",
    name: payload.couponType,
    description: payload.description || "",
    discountConfiguration: convertItems(
      payload.customConfiguration?.discount,
      payload.customConfiguration?.discountAdditional
    ),
    filterConfiguration: convertItems(
      payload.customConfiguration?.filter,
      payload.customConfiguration?.filterAdditional
    ),
    validityConfiguration: convertItems(
      payload.customConfiguration?.validity,
      payload.customConfiguration?.validityAdditional
    ),
  };
}

// Helper function to generate unique IDs
const generateId = () =>
  Date.now().toString(36) + Math.random().toString(36).substring(2, 9);

export default function AddCoupon() {
  const { data: couponPreloadData } = useLoaderData<{
    data: CouponPreloadType;
  }>();
  const fetcher = useFetcher<{ data: CouponDetailsType; success: boolean }>();
  const navigate = useNavigate();
  const { showToast } = useToast();

  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (fetcher.data.success === true) {
        showToast("Coupon created successfully", "success");
        navigate("/sellerSetting/coupons");
      } else if (fetcher.data.success === false) {
        showToast("Failed to create coupon", "error");
      }
    }
  }, [fetcher.state, fetcher.data]);

  const [payload, setPayload] = useState<CouponPayloadType>({
    couponType: "",
    customConfiguration: {
      discount: [],
      filter: [],
      validity: [],
      discountAdditional: [],
      filterAdditional: [],
      validityAdditional: [],
    },
  });

  // Get the selected coupon type data
  const selectedTypeData = payload.couponType
    ? couponPreloadData.couponTypesConfigurations.find(
        (type) => type.value === payload.couponType
      )
    : null;

  // Initialize configuration when coupon type changes
  useEffect(() => {
    if (!selectedTypeData) return;

    const initSection = (section: ConfigKey): IdConfigItem[] => {
      const props = selectedTypeData.configuration[section].properties;
      if (props.length === 0) return [];

      return [
        {
          id: generateId(),
          property: props[0].value,
          operator: props[0].operatorConfig?.options?.[0]?.value || "",
          value: "",
        },
      ];
    };

    // Initialize fixed inputs from propertyConfig
    const initFixedInputs = (section: ConfigKey): IdConfigItem[] => {
      const fixedInputs: IdConfigItem[] = [];

      // Check if section has propertyConfig
      if (selectedTypeData.configuration[section].propertyConfig) {
        selectedTypeData.configuration[section].propertyConfig.forEach(
          (config) => {
            if (config.type === "fixed") {
              fixedInputs.push({
                id: generateId(),
                property: config.value,
                value: "",
              });
            }
          }
        );
      }

      return fixedInputs;
    };

    setPayload((prev) => ({
      ...prev,
      customConfiguration: {
        discount: initSection("discount"),
        filter: initSection("filter"),
        validity: initSection("validity"),
        discountAdditional: initFixedInputs("discount"),
        filterAdditional: initFixedInputs("filter"),
        validityAdditional: initFixedInputs("validity"),
      },
    }));
  }, [payload.couponType, selectedTypeData]);

  // Get available properties for a section (excluding already selected ones)
  const getAvailableProperties = (section: ConfigKey) => {
    if (!selectedTypeData) return [];

    if (section === "filter") {
      // For filter section, return all properties
      return selectedTypeData.configuration[section].properties;
    }

    // For discount and validity sections, filter out already selected properties
    const selectedProperties =
      payload.customConfiguration?.[section].map((item) => item.property) || [];

    return selectedTypeData.configuration[section].properties.filter(
      (prop) => !selectedProperties.includes(prop.value)
    );
  };

  // Handle property change
  const handlePropertyChange = (
    section: ConfigKey,
    id: string,
    value: string
  ) => {
    if (!payload.customConfiguration) return;

    // Find the current item
    const currentItem = payload.customConfiguration[section].find(
      (item) => item.id === id
    );

    // If the property is already set to this value, no need to change
    if (currentItem?.property === value) {
      return;
    }

    // For discount and validity sections, check if the property is already selected in other rows
    if (section === "discount" || section === "validity") {
      const isAlreadySelected = payload.customConfiguration[section].some(
        (item) => item.id !== id && item.property === value
      );

      if (isAlreadySelected) {
        console.warn(
          `Property ${value} is already selected in ${section} section`
        );
        return;
      }
    }

    const sectionConfig = [...payload.customConfiguration[section]];
    const itemIndex = sectionConfig.findIndex((item) => item.id === id);

    if (itemIndex === -1) return;

    // Find the property configuration
    const propertyConfig = selectedTypeData?.configuration[
      section
    ].properties.find((prop) => prop.value === value);

    // Update the property and reset operator and value
    sectionConfig[itemIndex] = {
      ...sectionConfig[itemIndex],
      property: value,
      operator:
        propertyConfig?.operatorConfig?.options?.[0]?.value || undefined,
      value: "",
    };

    setPayload({
      ...payload,
      customConfiguration: {
        ...payload.customConfiguration,
        [section]: sectionConfig,
      },
    });
  };

  // Handle operator change
  const handleOperatorChange = (
    section: ConfigKey,
    id: string,
    value: string
  ) => {
    if (!payload.customConfiguration) return;

    const sectionConfig = [...payload.customConfiguration[section]];
    const itemIndex = sectionConfig.findIndex((item) => item.id === id);

    if (itemIndex === -1) return;

    sectionConfig[itemIndex] = {
      ...sectionConfig[itemIndex],
      operator: value,
    };

    setPayload({
      ...payload,
      customConfiguration: {
        ...payload.customConfiguration,
        [section]: sectionConfig,
      },
    });
  };

  // Handle value change
  const handleValueChange = (
    section: ConfigKey | ConfigAdditionalKey,
    id: string,
    value: string
  ) => {
    if (!payload.customConfiguration) return;

    const sectionConfig = [...(payload.customConfiguration[section] || [])];
    const itemIndex = sectionConfig.findIndex((item) => item.id === id);

    if (itemIndex === -1) return;

    sectionConfig[itemIndex] = {
      ...sectionConfig[itemIndex],
      value: value,
    };

    setPayload({
      ...payload,
      customConfiguration: {
        ...payload.customConfiguration,
        [section]: sectionConfig,
      },
    });
  };

  // Handle date change
  const handleDateChange = (
    section: ConfigKey,
    id: string,
    date: Date | undefined
  ) => {
    if (!payload.customConfiguration || !date) return;

    const sectionConfig = [...payload.customConfiguration[section]];
    const itemIndex = sectionConfig.findIndex((item) => item.id === id);

    if (itemIndex === -1) return;

    sectionConfig[itemIndex] = {
      ...sectionConfig[itemIndex],
      value: format(date, "yyyy-MM-dd HH:mm:ss"),
    };

    setPayload({
      ...payload,
      customConfiguration: {
        ...payload.customConfiguration,
        [section]: sectionConfig,
      },
    });
  };

  // Add a new row to a section
  const addRow = (section: ConfigKey) => {
    if (!payload.customConfiguration || !selectedTypeData) return;

    const properties = selectedTypeData.configuration[section].properties;
    if (properties.length === 0) return;

    // For discount and validity sections, check if we've reached the maximum number of rows
    if (section === "discount" || section === "validity") {
      const currentRows = payload.customConfiguration[section].length;
      const totalProperties = properties.length;

      if (currentRows >= totalProperties) {
        console.warn(
          `Cannot add more rows to ${section} section. Maximum reached.`
        );
        return;
      }

      // Get the first available property that's not already selected
      const availableProperties = getAvailableProperties(section);
      if (availableProperties.length === 0) {
        console.warn(`No more available properties for ${section} section`);
        return;
      }

      const newProperty = availableProperties[0].value;
      const newOperator =
        availableProperties[0].operatorConfig?.options?.[0]?.value;

      const newItem: IdConfigItem = {
        id: generateId(),
        property: newProperty,
        operator: newOperator,
        value: "",
      };

      setPayload({
        ...payload,
        customConfiguration: {
          ...payload.customConfiguration,
          [section]: [...payload.customConfiguration[section], newItem],
        },
      });
    } else {
      // For filter section, no restrictions
      const newItem: IdConfigItem = {
        id: generateId(),
        property: properties[0].value,
        operator: properties[0].operatorConfig?.options?.[0]?.value,
        value: "",
      };

      setPayload({
        ...payload,
        customConfiguration: {
          ...payload.customConfiguration,
          [section]: [...payload.customConfiguration[section], newItem],
        },
      });
    }
  };

  // Remove a row from a section
  const removeRow = (section: ConfigKey, id: string) => {
    if (!payload.customConfiguration) return;

    const sectionConfig = payload.customConfiguration[section].filter(
      (item) => item.id !== id
    );

    if (sectionConfig.length === 0) {
      // Prevent removing the last row, or re-add a default one
      addRow(section);
      return;
    }

    setPayload({
      ...payload,
      customConfiguration: {
        ...payload.customConfiguration,
        [section]: sectionConfig,
      },
    });
  };

  // Get the unit for a property
  const getPropertyUnit = (property: string) => {
    return DISCOUNT_UNIT_MAP[property] || "";
  };

  // Render operator input based on input type
  const renderOperatorInput = (
    section: ConfigKey,
    item: IdConfigItem,
    property: any
  ) => {
    if (!property?.operatorConfig) return null;

    const inputType = property.operatorConfig.input;

    switch (inputType) {
      case "dropdown":
        return (
          <Select
            value={item.operator || ""}
            onValueChange={(value) =>
              handleOperatorChange(section, item.id, value)
            }
          >
            <SelectTrigger className="rounded-lg">
              <SelectValue placeholder="Operator" />
            </SelectTrigger>
            <SelectContent className="rounded-lg">
              {property.operatorConfig.options?.map((option: any) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      case "text":
        return (
          <Input
            type="text"
            value={item.operator || ""}
            onChange={(e) =>
              handleOperatorChange(section, item.id, e.target.value)
            }
            placeholder="Operator"
          />
        );
      case "itemSearch":
        return (
          <Input
            type="text"
            value={item.operator || ""}
            onChange={(e) =>
              handleOperatorChange(section, item.id, e.target.value)
            }
            placeholder="Search item..."
          />
        );
      default:
        return (
          <Input
            type="text"
            value={item.operator || ""}
            onChange={(e) =>
              handleOperatorChange(section, item.id, e.target.value)
            }
            placeholder="Operator"
          />
        );
    }
  };

  // Render input based on input type
  const renderValueInput = (
    section: ConfigKey,
    item: IdConfigItem,
    inputType: string
  ) => {
    const property = selectedTypeData?.configuration[section].properties.find(
      (prop) => prop.value === item.property
    );

    switch (inputType) {
      case "text":
        return (
          <div className="relative">
            <Input
              type="text"
              value={item.value || ""}
              onChange={(e) =>
                handleValueChange(section, item.id, e.target.value)
              }
              className="pr-7"
            />
            {getPropertyUnit(item.property) && (
              <div className="absolute right-3 top-1/2 -translate-y-1/2 text-sm pointer-events-none text-muted-foreground">
                {getPropertyUnit(item.property)}
              </div>
            )}
          </div>
        );
      case "date":
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                type="button"
                variant="outline"
                className="rounded-lg w-full justify-between overflow-hidden"
              >
                {item.value
                  ? format(new Date(item.value), "PPP")
                  : "Pick a date"}
                <CalendarIcon className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={item.value ? new Date(item.value) : undefined}
                onSelect={(date) => handleDateChange(section, item.id, date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        );
      case "userSearch":
        return (
          <Input
            type="text"
            value={item.operator || ""}
            onChange={(e) =>
              handleOperatorChange(section, item.id, e.target.value)
            }
            placeholder="Search user..."
          />
        );
      case "location":
        // For simplicity, we'll use a text input for these special types
        return (
          <Input
            type="text"
            value={item.value || ""}
            onChange={(e) =>
              handleValueChange(section, item.id, e.target.value)
            }
            placeholder={`Search ${inputType.replace("Search", "")}`}
          />
        );
      case "dropdown":
        return (
          <Select
            value={item.value || ""}
            onValueChange={(value) =>
              handleValueChange(section, item.id, value)
            }
          >
            <SelectTrigger className="rounded-lg">
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent className="rounded-lg">
              {property?.valueConfig?.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      default:
        return (
          <Input
            type="text"
            value={item.value || ""}
            onChange={(e) =>
              handleValueChange(section, item.id, e.target.value)
            }
          />
        );
    }
  };

  // Render fixed inputs from propertyConfig
  const renderFixedInputs = (section: ConfigKey) => {
    if (!selectedTypeData || !payload.customConfiguration) return null;

    const propertyConfig =
      selectedTypeData.configuration[section].propertyConfig;
    if (!propertyConfig) return null;

    const fixedInputs = propertyConfig.filter(
      (input) => input.type === "fixed"
    );
    if (fixedInputs.length === 0) return null;

    return (
      <div className="mt-4">
        {fixedInputs.map((input) => {
          // Find the corresponding item in payload Additionals
          const item = payload.customConfiguration?.[
            configKeysEnum[section]
          ]?.find((item) => item.property === input.value);

          if (!item) return null;

          return (
            <div key={item.id} className="mt-2">
              <div className="flex flex-row gap-2 items-center">
                <label className="text-sm font-medium">{input.label}:</label>
                <div>
                  <Input
                    type="text"
                    value={item.value || ""}
                    onChange={(e) =>
                      handleValueChange(
                        configKeysEnum[section],
                        item.id,
                        e.target.value
                      )
                    }
                  />
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  // Render a configuration section
  const renderConfigSection = (section: ConfigKey) => {
    if (!selectedTypeData || !payload.customConfiguration) return null;

    const sectionConfig = selectedTypeData.configuration[section];
    const items = payload.customConfiguration[section];

    // Find property method config if it exists
    const propertyMethodInput = sectionConfig.propertyConfig?.find(
      (config) => config.type === "propertyMethod"
    );

    // Check if we can add more rows for this section
    const canAddMoreRows =
      section === "filter" || items.length < sectionConfig.properties.length;

    return (
      <div className="p-3 pr-8 rounded-lg border mb-3">
        <p className="text-typography-800 font-semibold">
          {sectionConfig.label} Configurations
        </p>
        {sectionConfig.description && (
          <p className="text-sm font-medium text-blue-600">
            {sectionConfig.description}
          </p>
        )}

        {items.map((item, index) => {
          const property = sectionConfig.properties.find(
            (prop) => prop.value === item.property
          );

          const hasOperator = !!property?.operatorConfig;
          const valueInputType = property?.valueConfig?.input || "text";

          // For discount and validity sections, only show properties that aren't already selected
          // or the current property of this row
          const availableProperties =
            section === "filter"
              ? sectionConfig.properties
              : sectionConfig.properties.filter(
                  (prop) =>
                    prop.value === item.property ||
                    !payload.customConfiguration?.[section].some(
                      (i) => i.id !== item.id && i.property === prop.value
                    )
                );

          return (
            <div key={item.id} className="mt-3">
              <div
                className={`relative grid ${
                  hasOperator ? "grid-cols-3" : "grid-cols-2"
                } gap-2 items-center`}
              >
                <Select
                  value={item.property}
                  onValueChange={(value) =>
                    handlePropertyChange(section, item.id, value)
                  }
                >
                  <SelectTrigger className="rounded-lg">
                    <SelectValue placeholder="Property" />
                  </SelectTrigger>
                  <SelectContent className="rounded-lg">
                    {availableProperties.map((property) => (
                      <SelectItem key={property.value} value={property.value}>
                        {property.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {hasOperator && renderOperatorInput(section, item, property)}

                {renderValueInput(section, item, valueInputType)}

                {items.length > 1 && (
                  <div
                    className="absolute -right-7 top-1/2 -translate-y-1/2 px-1 py-2 cursor-pointer hover:bg-accent rounded-md"
                    onClick={() => removeRow(section, item.id)}
                  >
                    <Trash2Icon className="h-5 w-5 text-red-500" />
                  </div>
                )}
              </div>
              {index === 0 && propertyMethodInput && (
                <PropertyMethodInput
                  section={section}
                  propertyInput={propertyMethodInput}
                  payload={payload}
                  setPayload={setPayload}
                  selectedTypeData={selectedTypeData}
                />
              )}
            </div>
          );
        })}

        {canAddMoreRows && (
          <Button
            type="button"
            className="mt-3 underline"
            variant="link"
            onClick={() => addRow(section)}
          >
            <PlusIcon className="w-4 h-4 mr-1" /> Add another{" "}
            {section.toLowerCase()}
          </Button>
        )}

        {/* Render fixed inputs at the bottom of the section */}
        {renderFixedInputs(section)}
      </div>
    );
  };

  return (
    <div>
      <div>
        <div
          aria-labelledby="add-coupon-heading"
          className="flex flex-row gap-2 items-center p-2"
        >
          <div
            className="w-5 h-5 cursor-pointer"
            onClick={() => navigate("/sellerSetting/coupons")}
          >
            <svg
              width="100%"
              height="100%"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M7.13805 3.36193C7.3984 3.62228 7.3984 4.04439 7.13805 4.30474L3.60946 7.83333H14C14.3682 7.83333 14.6666 8.13181 14.6666 8.5C14.6666 8.86819 14.3682 9.16667 14 9.16667H3.60946L7.13805 12.6953C7.3984 12.9556 7.3984 13.3777 7.13805 13.6381C6.8777 13.8984 6.45559 13.8984 6.19524 13.6381L1.52858 8.97141C1.26823 8.71106 1.26823 8.28895 1.52858 8.0286L6.19524 3.36193C6.45559 3.10158 6.8777 3.10158 7.13805 3.36193Z"
                fill="#1F2A37"
              />
            </svg>
          </div>
          <h2 className="text-lg font-semibold text-typography-700">
            Add new coupon
          </h2>
        </div>
      </div>

      <div className="pt-3 pb-20">
        <fetcher.Form method="post">
          <input type="hidden" name="actionType" value="addCoupon" />
          <input type="hidden" name="payload" value={JSON.stringify(payload)} />
          <div aria-labelledby="add-coupon" className="p-2">
            <div className="p-3 rounded-lg border mb-3">
              <p className="font-semibold text-typography-800">
                Coupon Details
              </p>
              <p className="text-sm font-medium text-blue-600">
                These details would be shown to the customers.
              </p>
              <div className="relative mt-3 flex flex-row gap-5 items-center justify-between">
                <p className="whitespace-nowrap">Coupon type:</p>
                <Select
                  value={payload.couponType || ""}
                  onValueChange={(value) => {
                    setPayload({
                      ...payload,
                      couponType: value,
                    });
                  }}
                >
                  <SelectTrigger className="rounded-lg">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent className="rounded-lg">
                    {couponPreloadData.couponTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Input
                type="text"
                placeholder="Coupon Code"
                className="mt-3 rounded-lg"
                value={payload.couponCode || ""}
                onChange={(e) => {
                  setPayload({
                    ...payload,
                    couponCode: e.target.value,
                  });
                }}
              />
              <Input
                type="text"
                placeholder="Description"
                className="mt-3 rounded-lg"
                value={payload.description || ""}
                onChange={(e) => {
                  setPayload({
                    ...payload,
                    description: e.target.value,
                  });
                }}
              />
            </div>

            {selectedTypeData && (
              <>
                {renderConfigSection("discount")}
                {renderConfigSection("filter")}
                {renderConfigSection("validity")}
              </>
            )}
          </div>

          <div className="fixed bottom-2 left-0 right-0 z-30 flex justify-center md:static md:mt-6 md:px-4 md:justify-end">
            <Button
              type="submit"
              className="w-[80%] md:w-auto md:px-12 pointer-events-auto font-bold flex items-center justify-center rounded-lg shadow-xl bg-primary p-3 hover:bg-primary"
            >
              Save
            </Button>
          </div>
        </fetcher.Form>
      </div>
    </div>
  );
}

interface PropertyMethodInputProps {
  section: ConfigKey;
  propertyInput: {
    input: string;
    type: string;
    label: string;
    value: string;
    options?: { label: string; value: string }[];
  };
  payload: CouponPayloadType;
  setPayload: React.Dispatch<React.SetStateAction<CouponPayloadType>>;
  selectedTypeData?: CouponPreloadType["couponTypesConfigurations"][number];
}

const PropertyMethodInput: React.FC<PropertyMethodInputProps> = ({
  section,
  propertyInput,
  payload,
  setPayload,
  selectedTypeData,
}) => {
  const shouldRender =
    section === "discount" &&
    propertyInput.type === "propertyMethod" &&
    payload.customConfiguration?.discount?.length === 2;

  const configItem = useMemo(() => {
    return (
      payload.customConfiguration?.discountAdditional?.find(
        (item) => item.property === propertyInput.value
      ) || {
        id: generateId(),
        property: propertyInput.value,
        value: propertyInput.options?.[0]?.value || "",
      }
    );
  }, [payload, propertyInput]);

  const handleValueChange = (newValue: string) => {
    const updatedAdditional = [
      ...(payload.customConfiguration?.discountAdditional || []).filter(
        (item) => item.property !== propertyInput.value
      ),
      {
        id: configItem.id,
        property: propertyInput.value,
        value: newValue,
      },
    ];

    setPayload({
      ...payload,
      customConfiguration: {
        discount: payload.customConfiguration?.discount || [],
        filter: payload.customConfiguration?.filter || [],
        validity: payload.customConfiguration?.validity || [],
        discountAdditional: updatedAdditional,
      },
    });
  };

  // Manage add/remove logic on mount/unmount or discount length change
  useEffect(() => {
    if (!payload.customConfiguration) return;

    const discountItems = payload.customConfiguration.discount;
    const propertyMethodConfig =
      selectedTypeData?.configuration.discount.propertyConfig?.find(
        (config) => config.type === "propertyMethod"
      );
    if (!propertyMethodConfig) return;

    const currentAdditional = [
      ...(payload.customConfiguration.discountAdditional || []),
    ];
    const hybDiscStrategyIndex = currentAdditional.findIndex(
      (item) => item.property === propertyMethodConfig.value
    );

    // Add if not present and condition met
    if (discountItems.length === 2 && hybDiscStrategyIndex === -1) {
      currentAdditional.push({
        id: generateId(),
        property: propertyMethodConfig.value,
        value: propertyMethodConfig.options?.[0]?.value || "",
      });
      setPayload((prev) => ({
        ...prev,
        customConfiguration: {
          ...prev.customConfiguration,
          discount: prev.customConfiguration?.discount || [],
          filter: prev.customConfiguration?.filter || [],
          validity: prev.customConfiguration?.validity || [],
          discountAdditional: currentAdditional,
        },
      }));
    }

    // Remove if condition fails
    if (discountItems.length !== 2 && hybDiscStrategyIndex !== -1) {
      currentAdditional.splice(hybDiscStrategyIndex, 1);
      setPayload((prev) => ({
        ...prev,
        customConfiguration: {
          ...prev.customConfiguration,
          discount: prev.customConfiguration?.discount || [],
          filter: prev.customConfiguration?.filter || [],
          validity: prev.customConfiguration?.validity || [],
          discountAdditional: currentAdditional,
        },
      }));
    }
  }, [payload.customConfiguration?.discount?.length]);

  if (!shouldRender) return null;

  return (
    <div className="my-2">
      <Tabs value={configItem.value} onValueChange={handleValueChange}>
        <TabsList>
          {propertyInput.options?.map((option) => (
            <TabsTrigger
              key={option.value}
              value={option.value}
              className="flex-1"
            >
              {option.label}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
    </div>
  );
};
