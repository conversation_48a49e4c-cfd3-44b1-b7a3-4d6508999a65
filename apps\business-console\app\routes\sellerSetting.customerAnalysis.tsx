import { LoaderFunction } from "@remix-run/node";
import { useLoaderData, useNavigate, useOutletContext } from "@remix-run/react";
import { Menu, MoveLeft, RotateCw } from "lucide-react";
// eslint-disable-next-line import/namespace, import/default
import ConversionRate from "~/components/common/ConversionRate";
import CustomersAcquisition from "~/components/common/CustomerAcquisation";
import CustomerSales from "~/components/common/CustomerSales";
import LocalityAnalysis from "~/components/common/LocalityAnalysis";
import { Button } from "~/components/ui/button";
import { getAcquiSitionRates, getConversionRate, getSellerSales } from "~/services/buyerSetting";
import { CustomerAcquisition, CustomerConversionRate, SellerSales } from "~/types/api/businessConsoleService/BuyerAccountingResponse";
import { withAuth, withResponse } from "~/utils/auth-utils";



interface LoaderData {
  sales: SellerSales,
  customerConversionRates: CustomerConversionRate,
  customerAcquisitionRate: CustomerAcquisition,
  week: number
}


export const loader: LoaderFunction = withAuth(async ({ request }) => {
  try {
    const week = 4;

    // Make parallel API calls
    const [salesResponse, conversionResponse, acquisitionResponse] = await Promise.all([
      getSellerSales(week, request),
      getConversionRate(3, request),
      getAcquiSitionRates(week, request)
    ]);
    const responseHeaders = new Headers();
    [salesResponse, conversionResponse, acquisitionResponse].forEach(response => {
      if (response.headers?.has('Set-Cookie')) {
        responseHeaders.set('Set-Cookie', response.headers.get('Set-Cookie')!);
      }
    });

    return withResponse({
      sales: salesResponse.data,
      customerConversionRates: conversionResponse.data,
      customerAcquisitionRate: acquisitionResponse.data,
      week: week
    }, responseHeaders);
  }
  catch (error) {
    throw new Response("Failed to fetch customer analysis data", {
      status: 500,
      statusText: error instanceof Error ? error.message : "Unknown error"
    });
  }
})




export default function CustomerAnalysis() {

  const { sales, customerConversionRates, customerAcquisitionRate, week } = useLoaderData<LoaderData>()
  const navigate = useNavigate();

  const { setIsOpen } = useOutletContext<{ setIsOpen: (open: boolean) => void }>()
  return (
    <div className="w-full min-h-screen bg-slate-100">
      <header className="flex justify-between items-center shadow-md bg-white p-3 w-full sticky top-0 z-10 px-4">
        <h1 className="text-black text-lg md:text-xl font-semibold ">
          My Dashboard
        </h1>
        <Button
          variant='ghost'
          onClick={() => setIsOpen(true)}
        >
          <Menu className="h-6 w-6" />
        </Button>
      </header>
      <main className="p-2">
        <section >
          <CustomerSales sales={sales} />
        </section>
        <section >
          <ConversionRate conversionRates={customerConversionRates} week={week} />
        </section>
        <section >
          <CustomersAcquisition customerAcquisitionRate={customerAcquisitionRate} />
        </section>
        {/* <section>
          <LocalityAnalysis />
        </section> */}
      </main>
    </div>
  )
}