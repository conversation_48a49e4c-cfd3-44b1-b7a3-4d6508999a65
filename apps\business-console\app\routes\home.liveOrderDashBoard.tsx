import type React from "react"

import { useState, useEffect } from "react"
import { Badge } from "~/components/ui/badge"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { Card, CardContent } from "~/components/ui/card"
import { Input } from "~/components/ui/input"
import { Label } from "~/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select"
import { Clock, AlertCircle, Search, Filter, Phone, Store, Truck, MapPin, User, Eye, Package, AlertTriangle, MessageCircleQuestionIcon } from "lucide-react"
import { usePolling } from "~/hooks/usePolling"
import type { rNETOrder, OrderStatus } from "~/types/api/businessConsoleService/rNETOrder"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "~/components/ui/dialog"
import { Separator } from "~/components/ui/separator"
import { Textarea } from "@headlessui/react"
import { json, <PERSON><PERSON>Function, ActionFunction } from "@remix-run/node"
import { useLoaderData } from "@remix-run/react"
import { withAuth, withResponse } from "~/utils/auth-utils"
import { getrNETOrders } from "~/services/rnetOrders"


export const loader: LoaderFunction = withAuth(async ({ request, user }) => {
  const url = new URL(request.url);
  const id = Number(url.searchParams.get("id"));

  let data;
  try {
    const response = await getrNETOrders(request);
    data = response?.data || {};
    return withResponse(data, response?.headers);
  } catch (error) {
    console.error("Error in loader:", error);
    // Return a JSON-based error shape
    return {};
  }
});


type ActionIntent = "Update Order Status" | "Update Logistic Status" | "Cancel Order";
interface ActionData {
  intent: ActionIntent;
  errorMessage: string;
  success: boolean;
}
export const action: ActionFunction = withAuth(async ({ request }) => {
  const formData = await request.formData();
  const intent = formData.get("intent");
  const id = formData.get("id") as unknown as number;
  const data = formData.get("data") as string | undefined;

  if (!intent) {
    return json({ success: false, errorMessage: "Invalid request", intent: intent }, { status: 400 });
  }
  if (intent === "Update Order Status") {
    try {
      const response = await {};
      return withResponse({ success: true, intent: intent }, response.headers);
    }
    catch (error) {
      return json({ success: false, intent: intent, errorMessage: "Failed to update order status" }, { status: 400 })
    }
  }
  if (intent === "Cancel Order") {
    try {
      const response = await {};
      return withResponse({ success: true, intent: intent }, response.headers);
    }
    catch (error) {
      return json({ success: false, intent: intent, errorMessage: "Failed to cancel order" }, { status: 400 })
    }
  }

  return json({ success: false, intent: intent, errorMessage: "Invalid intent" }, { status: 400 });
});


export default function LiveOrderDashboard() {
  const { orders } = useLoaderData<{ orders: rNETOrder[] }>()
  const [selectedOrder, setSelectedOrder] = useState<rNETOrder | null>(null)

  const [actionType, setActionType] = useState<string>("")
  const [actionSelectedOrder, setActionSelectedOrder] = useState<rNETOrder | null>(null)

  // Filters
  const [searchTerm, setSearchTerm] = useState("")
  const [filterDate, setFilterDate] = useState("")
  const [filterStatus, setFilterStatus] = useState<OrderStatus | "all">("all")
  const [filterDeliveryType, setFilterDeliveryType] = useState<"MP2" | "SELF" | "all">("all")
  const [selectedSeller, setSelectedSeller] = useState("all")

  // Use polling hook for auto-refresh
  const { isPolling, startPolling, stopPolling } = usePolling(refreshOrders, 10000)
  function refreshOrders() {
    console.log("Refreshing orders...")
  }

  useEffect(() => {
    startPolling()
    return () => stopPolling()
  }, [startPolling, stopPolling])

  const statusFilters = [
    { label: "All Orders", value: "all" },
    { label: "Created", value: "Created" },
    { label: "Accepted", value: "Accepted" },
    { label: "Packed", value: "Packed" },
    { label: "Assigned", value: "Assigned" },
    { label: "Picked Up", value: "PickedUp" },
    { label: "Dispatched", value: "Dispatched" },
    { label: "Delivered", value: "Delivered" },
    { label: "Cancelled", value: "Cancelled" },
  ]

  const deliveryTypeFilters = [
    { label: "All Types", value: "all" },
    { label: "MP2 Delivery", value: "MP2" },
    { label: "Self Delivery", value: "SELF" },
  ]

  // Filter orders based on all criteria
  const filteredOrders = orders?.filter((order) => {
    // Date filter
    const orderDate = new Date(order.createdAt).toISOString().slice(0, 10)
    if (filterDate && orderDate !== filterDate) return false

    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase()
      const matchesSearch =
        order.orderGroupId.toString().includes(searchTerm) ||
        order.buyerName.toLowerCase().includes(searchLower) ||
        order.buyerMobile.includes(searchTerm) ||
        order.sellerName.toLowerCase().includes(searchLower) ||
        order.sellerMobile.includes(searchTerm) ||
        order.logisticDetails?.riderName?.toLowerCase().includes(searchLower) ||
        order.logisticDetails?.riderPhone?.includes(searchTerm)

      if (!matchesSearch) return false
    }

    // Status filter
    if (filterStatus !== "all" && order.orderStatus !== filterStatus) return false

    // Delivery type filter
    if (filterDeliveryType !== "all" && order.logisticProvider !== filterDeliveryType) return false

    return true
  })

  // Separate live and completed orders
  const liveOrders = filteredOrders?.filter((order) => !["Delivered", "Cancelled"].includes(order.orderStatus))

  const completedOrders = filteredOrders?.filter((order) => ["Delivered", "Cancelled"].includes(order.orderStatus))

  // Get unique sellers for filter dropdown
  const uniqueSellers = Array.from(
    new Map(orders.map((order) => [order.sellerId, { id: order.sellerId, name: order.sellerName }])).values(),
  )

  // Statistics
  const stats = {
    total: orders?.length,
    created: orders?.filter((o) => o.orderStatus === "Created").length,
    accepted: orders?.filter((o) => o.orderStatus === "Accepted").length,
    packed: orders?.filter((o) => o.orderStatus === "Packed").length,
    assigned: orders?.filter((o) => o.orderStatus === "Assigned").length,
    dispatched: orders?.filter((o) => o.orderStatus === "Dispatched").length,
    delivered: orders?.filter((o) => o.orderStatus === "Delivered").length,
    cancelled: orders?.filter((o) => o.orderStatus === "Cancelled").length,
  }

  const handleAction = (order: rNETOrder, action: string) => {
    setActionSelectedOrder(order)
    setActionType(action)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        {/* Header */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Live Order Dashboard</h1>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <div className={`w-2 h-2 rounded-full ${isPolling ? "bg-green-500 animate-pulse" : "bg-gray-400"}`} />
              Auto-refresh {isPolling ? "ON" : "OFF"}
            </div>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-8 gap-3 sm:gap-4 mb-6">
            <Card className="shadow-sm">
              <CardContent className="p-3 sm:p-4 text-center">
                <div className="text-lg sm:text-xl font-semibold text-gray-900">{stats.total}</div>
                <div className="text-xs sm:text-sm text-gray-600">Total</div>
              </CardContent>
            </Card>
            <Card className="shadow-sm">
              <CardContent className="p-3 sm:p-4 text-center">
                <div className="text-lg sm:text-xl font-semibold text-purple-600">{stats.created}</div>
                <div className="text-xs sm:text-sm text-gray-600">Created</div>
              </CardContent>
            </Card>
            <Card className="shadow-sm">
              <CardContent className="p-3 sm:p-4 text-center">
                <div className="text-lg sm:text-xl font-semibold text-purple-600">{stats.accepted}</div>
                <div className="text-xs sm:text-sm text-gray-600">Accepted</div>
              </CardContent>
            </Card>
            <Card className="shadow-sm">
              <CardContent className="p-3 sm:p-4 text-center">
                <div className="text-lg sm:text-xl font-semibold text-orange-600">{stats.packed}</div>
                <div className="text-xs sm:text-sm text-gray-600">Packed</div>
              </CardContent>
            </Card>
            <Card className="shadow-sm">
              <CardContent className="p-3 sm:p-4 text-center">
                <div className="text-lg sm:text-xl font-semibold text-orange-600">{stats.assigned}</div>
                <div className="text-xs sm:text-sm text-gray-600">Assigned</div>
              </CardContent>
            </Card>
            <Card className="shadow-sm">
              <CardContent className="p-3 sm:p-4 text-center">
                <div className="text-lg sm:text-xl font-semibold text-yellow-600">{stats.dispatched}</div>
                <div className="text-xs sm:text-sm text-gray-600">Dispatched</div>
              </CardContent>
            </Card>
            <Card className="shadow-sm">
              <CardContent className="p-3 sm:p-4 text-center">
                <div className="text-lg sm:text-xl font-semibold text-green-600">{stats.delivered}</div>
                <div className="text-xs sm:text-sm text-gray-600">Delivered</div>
              </CardContent>
            </Card>
            <Card className="shadow-sm">
              <CardContent className="p-3 sm:p-4 text-center">
                <div className="text-lg sm:text-xl font-semibold text-red-600">{stats.cancelled}</div>
                <div className="text-xs sm:text-sm text-gray-600">Cancelled</div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="space-y-4">
              {/* Search Bar */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search by Order ID, Customer, Seller, or Phone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Filters */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Date</Label>
                  <Input
                    type="date"
                    value={filterDate}
                    onChange={(e) => setFilterDate(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">Status</Label>
                  <Select value={filterStatus} onValueChange={(value: OrderStatus | "all") => setFilterStatus(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {statusFilters.map((filter) => (
                        <SelectItem key={filter.value} value={filter.value}>
                          {filter.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">Delivery Type</Label>
                  <Select
                    value={filterDeliveryType}
                    onValueChange={(value: "MP2" | "SELF" | "all") => setFilterDeliveryType(value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {deliveryTypeFilters.map((filter) => (
                        <SelectItem key={filter.value} value={filter.value}>
                          {filter.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">Sellers</Label>
                  <Select
                    value={selectedSeller}
                    onValueChange={(value) => setSelectedSeller(value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all" >All Sellers</SelectItem>
                      {uniqueSellers.map((seller) => (
                        <SelectItem key={seller.id} value={seller.id.toString()}>
                          {seller.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button
                    onClick={() => {
                      setSearchTerm("")
                      setFilterDate("")
                      setFilterStatus("all")
                      setFilterDeliveryType("all")
                      setSelectedSeller("all")
                    }}
                    className="w-full"
                  >
                    <Filter className="w-4 h-4 mr-2" />
                    Clear Filters
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Live Orders Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl sm:text-2xl font-bold flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
              Live Orders
              <Badge variant="secondary" className="ml-2">
                {liveOrders.length}
              </Badge>
            </h2>
          </div>

          {liveOrders.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="text-gray-500">
                  <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium">No live orders</p>
                  <p className="text-sm">No active orders for the selected date and filters.</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
              {liveOrders.map((order) => (
                <OrderCard
                  key={order.orderGroupId}
                  order={order}
                  onViewDetails={setSelectedOrder}
                  onAction={handleAction}
                />
              ))}
            </div>
          )}
        </div>

        {/* Completed Orders Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl sm:text-2xl font-bold flex items-center gap-2">
              <div className="w-3 h-3 bg-gray-500 rounded-full" />
              Completed Orders
              <Badge variant="secondary" className="ml-2">
                {completedOrders.length}
              </Badge>
            </h2>
          </div>

          {completedOrders.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="text-gray-500">
                  <AlertCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium">No completed orders</p>
                  <p className="text-sm">No completed orders for the selected date and filters.</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="overflow-x-auto">
              <div className="flex gap-4 pb-4 min-w-max">
                {completedOrders.map((order) => (
                  <div key={order.orderGroupId} className="flex-shrink-0 w-72 sm:w-80">
                    <OrderCard order={order} onViewDetails={setSelectedOrder} onAction={handleAction} />
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Order Details Modal */}
        <OrderDetailsModal order={selectedOrder} onClose={() => setSelectedOrder(null)} onAction={handleAction} />

        {/* Action Modal */}
        <ActionModal
          order={actionSelectedOrder}
          actionType={actionType}
          onClose={() => {
            setActionSelectedOrder(null)
            setActionType("")
          }}
        />
      </div>
    </div>
  )
}


// Order Card
interface OrderCardProps {
  order: rNETOrder
  onViewDetails: (order: rNETOrder) => void
  onAction: (order: rNETOrder, action: string) => void
}

export function OrderCard({ order, onViewDetails, onAction }: OrderCardProps) {
  const [currentTime, setCurrentTime] = useState(Date.now())

  // Update timer every second for live orders
  useEffect(() => {
    if (!["Delivered", "Cancelled"].includes(order.orderStatus)) {
      const interval = setInterval(() => {
        setCurrentTime(Date.now())
      }, 1000)
      return () => clearInterval(interval)
    }
  }, [order.orderStatus])

  const styling = getOrderCardStyling(order)
  const timeDiff = getTimeDifferenceInSeconds(order.createdAt)
  const isNewOrder = timeDiff <= 60

  const handlePhoneClick = (phoneNumber: string, e: React.MouseEvent) => {
    e.stopPropagation()
    window.open(`tel:${phoneNumber}`, "_self")
  }

  const getActionButtons = () => {
    const buttons = []

    if (order.orderStatus === "Created") {
      buttons.push(
        <Button
          key="cancel"
          variant="destructive"
          size="sm"
          onClick={(e) => {
            e.stopPropagation()
            onAction(order, "cancel")
          }}
          className="text-xs"
        >
          Cancel Order
        </Button>,
      )
    }

    if ((order.orderStatus === "Accepted" || order.orderStatus === "Packed") && order.logisticProvider === "MP2") {
      buttons.push(
        <Button
          key="manual"
          variant="default"
          size="sm"
          onClick={(e) => {
            e.stopPropagation()
            onAction(order, "manual_fulfillment")
          }}
          className="text-xs"
        >
          Manual Fulfillment
        </Button>,
      )
    }

    return buttons
  }

  return (
    <Card
      className={`${styling.className} cursor-pointer transition-all duration-500 h-full hover:shadow-lg`}
      onClick={() => onViewDetails(order)}
      onMouseEnter={(e) => {
        e.currentTarget.style.animationPlayState = "paused"
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.animationPlayState = "running"
      }}
    >
      <CardContent className="p-3 sm:p-4">
        {/* Header with Order ID and Time */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 mb-3">
          <div className="flex flex-wrap items-center gap-2">
            <div className="font-semibold text-base sm:text-lg">#{order.orderGroupId}</div>
            {isNewOrder && (
              <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs animate-bounce">
                NEW
              </Badge>
            )}
            {order.supportTickets && order.supportTickets.length > 0 && (
              <Badge variant="destructive" className="flex items-center gap-1 text-xs">
                <MessageCircleQuestionIcon className="w-3 h-3" />
                {order.supportTickets.length}
              </Badge>
            )}
          </div>
          <div className="text-xs sm:text-sm text-gray-500 flex items-center gap-1">
            <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
            {["Delivered", "Cancelled"].includes(order.orderStatus)
              ? formatTimeElapsed(getTimeDifferenceInSeconds(order.createdAt, order.deliveredTime || order.updatedAt))
              : formatTimeElapsed(timeDiff)}
          </div>
        </div>

        {/* Customer Info */}
        <div className="mb-3">
          <div className="flex items-center gap-2 mb-1">
            <User className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0" />
            <span className="font-medium text-sm sm:text-base truncate">{order.buyerName}</span>
            <button
              onClick={(e) => handlePhoneClick(order.buyerMobile, e)}
              className="text-blue-600 hover:text-blue-800 flex-shrink-0 p-1 rounded hover:bg-blue-50"
            >
              <Phone className="w-3 h-3 sm:w-4 sm:h-4" />
            </button>
          </div>
          <div className="text-xs sm:text-sm text-gray-600 flex items-start gap-2">
            <MapPin className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 mt-0.5 flex-shrink-0" />
            <span className="line-clamp-2">{order.bAddress}</span>
          </div>
        </div>

        {/* Restaurant Info */}
        <div className="mb-3">
          <div className="flex items-center gap-2 flex-wrap">
            <Store className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0" />
            <span className="font-medium text-sm sm:text-base truncate">{order.sellerName}</span>
            <button
              onClick={(e) => handlePhoneClick(order.sellerMobile, e)}
              className="text-blue-600 hover:text-blue-800 flex-shrink-0 p-1 rounded hover:bg-blue-50"
            >
              <Phone className="w-3 h-3 sm:w-4 sm:h-4" />
            </button>
            <Badge variant="outline" className="text-xs">
              {order.logisticProvider}
            </Badge>
          </div>
        </div>

        {/* Delivery Info */}
        {order.logisticProvider === "MP2" && order.logisticDetails?.riderName && (
          <div className="mb-3">
            <div className="flex items-center gap-2 flex-wrap">
              <Truck className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0" />
              <span className="text-xs sm:text-sm truncate">{order.logisticDetails.riderName}</span>
              {order.logisticDetails.riderPhone && (
                <button
                  onClick={(e) => handlePhoneClick(order.logisticDetails.riderPhone.toString(), e)}
                  className="text-blue-600 hover:text-blue-800 flex-shrink-0 p-1 rounded hover:bg-blue-50"
                >
                  <Phone className="w-3 h-3 sm:w-4 sm:h-4" />
                </button>
              )}
              {order.logStatus && (
                <Badge variant="outline" className="text-xs">
                  {getLogisticStatusDisplayName(order.logStatus)}
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Order Summary */}
        <div className="mb-3">
          <div className="text-sm sm:text-base font-medium text-gray-900 mb-1">
            {order.totalItems} item(s) • ₹{order.totalOrderGroupAmount}
          </div>
          {order.orderItems && (
            <div className="text-xs text-gray-500 line-clamp-2">
              {order.orderItems.slice(0, 3).map((item, idx) => (
                <span key={item.inventoryId}>
                  {item.itemName} x{item.orderedQty}
                  {idx < Math.min(order.orderItems!.length, 3) - 1 ? ", " : ""}
                </span>
              ))}
              {order.orderItems.length > 3 && "..."}
            </div>
          )}
        </div>

        {/* Status and Actions */}
        <div className="flex flex-col gap-2">
          <div className="flex justify-between items-center">
            <Badge
              variant={
                order.orderStatus === "Delivered"
                  ? "default"
                  : order.orderStatus === "Cancelled"
                    ? "destructive"
                    : "secondary"
              }
              className="capitalize text-xs"
            >
              {getStatusDisplayName(order.orderStatus)}
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                onViewDetails(order)
              }}
              className="flex items-center gap-1 text-xs sm:text-sm"
            >
              <Eye className="w-3 h-3 sm:w-4 sm:h-4" />
              Details
            </Button>
          </div>

          {/* Action Buttons */}
          {getActionButtons().length > 0 && <div className="flex gap-2 flex-wrap">{getActionButtons()}</div>}
        </div>

        {/* Helper Text */}
        {styling.helperText && (
          <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-800">
            <AlertCircle className="w-3 h-3 inline mr-1" />
            {styling.helperText}
          </div>
        )}
      </CardContent>
    </Card>
  )
}


// Order Details Modal
interface OrderDetailsModalProps {
  order: rNETOrder | null
  onClose: () => void
  onAction: (order: rNETOrder, action: string) => void
}

export function OrderDetailsModal({ order, onClose, onAction }: OrderDetailsModalProps) {
  if (!order) return null

  const handlePhoneClick = (phoneNumber: string) => {
    window.open(`tel:${phoneNumber}`, "_self")
  }

  const timeDiff = getTimeDifferenceInSeconds(order.createdAt)

  return (
    <Dialog open={!!order} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto w-[95vw] sm:w-full">
        <DialogHeader>
          <DialogTitle className="text-lg sm:text-xl flex items-center gap-2">
            Order Details - #{order.orderGroupId}
            <Badge
              variant={
                order.orderStatus === "Delivered"
                  ? "default"
                  : order.orderStatus === "Cancelled"
                    ? "destructive"
                    : "secondary"
              }
              className="capitalize"
            >
              {getStatusDisplayName(order.orderStatus)}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Order Timeline */}
          <div>
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <Clock className="w-4 h-4" />
              Order Timeline
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Order Placed:</span>
                <span>{new Date(order.createdAt).toLocaleString()}</span>
              </div>
              {order.acceptedTime && (
                <div className="flex justify-between">
                  <span>Accepted:</span>
                  <span>{new Date(order.acceptedTime).toLocaleString()}</span>
                </div>
              )}
              {order.packedTime && (
                <div className="flex justify-between">
                  <span>Packed:</span>
                  <span>{new Date(order.packedTime).toLocaleString()}</span>
                </div>
              )}
              {order.assignedTime && (
                <div className="flex justify-between">
                  <span>Assigned:</span>
                  <span>{new Date(order.assignedTime).toLocaleString()}</span>
                </div>
              )}
              {order.pickedUpTime && (
                <div className="flex justify-between">
                  <span>Picked Up:</span>
                  <span>{new Date(order.pickedUpTime).toLocaleString()}</span>
                </div>
              )}
              {order.deliveryStartTime && (
                <div className="flex justify-between">
                  <span>Dispatched:</span>
                  <span>{new Date(order.deliveryStartTime).toLocaleString()}</span>
                </div>
              )}
              {order.deliveredTime && (
                <div className="flex justify-between">
                  <span>Delivered:</span>
                  <span>{new Date(order.deliveredTime).toLocaleString()}</span>
                </div>
              )}
              <div className="flex justify-between font-medium">
                <span>Total Time:</span>
                <span>
                  {["Delivered", "Cancelled"].includes(order.orderStatus)
                    ? formatTimeElapsed(
                      getTimeDifferenceInSeconds(order.createdAt, order.deliveredTime || order.updatedAt),
                    )
                    : formatTimeElapsed(timeDiff)}
                </span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Basic Order Info */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <Package className="w-4 h-4" />
                Order Information
              </h3>
              <div className="space-y-2 text-sm">
                <div>
                  <strong>Order ID:</strong> #{order.orderGroupId}
                </div>
                <div>
                  <strong>Network:</strong> {order.networkName}
                </div>
                <div>
                  <strong>Delivery Type:</strong> {order.deliveryType}
                </div>
                <div>
                  <strong>Logistics Provider:</strong> {order.logisticProvider}
                </div>
                {order.deliveryOtp && (
                  <div>
                    <strong>Delivery OTP:</strong> {order.deliveryOtp}
                  </div>
                )}
              </div>
            </div>

            <div>
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <User className="w-4 h-4" />
                Customer Information
              </h3>
              <div className="space-y-2 text-sm">
                <div>
                  <strong>Name:</strong> {order.buyerName}
                </div>
                <div className="flex items-center gap-2">
                  <strong>Phone:</strong>
                  <span>{order.buyerMobile}</span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handlePhoneClick(order.buyerMobile)}
                    className="h-6 w-6 p-0"
                  >
                    <Phone className="w-3 h-3" />
                  </Button>
                </div>
                <div>
                  <strong>Address:</strong> {order.bAddress}
                </div>
                <div>
                  <strong>Area:</strong> {order.bAreaName}
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Restaurant Info */}
          <div>
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <Store className="w-4 h-4" />
              Restaurant Information
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
              <div>
                <div>
                  <strong>Name:</strong> {order.sellerName}
                </div>
                <div className="flex items-center gap-2">
                  <strong>Phone:</strong>
                  <span>{order.sellerMobile}</span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handlePhoneClick(order.sellerMobile)}
                    className="h-6 w-6 p-0"
                  >
                    <Phone className="w-3 h-3" />
                  </Button>
                </div>
              </div>
              <div>
                {order.agentName && (
                  <div>
                    <strong>Agent:</strong> {order.agentName}
                  </div>
                )}
                {order.sellerMessage && (
                  <div>
                    <strong>Message:</strong> {order.sellerMessage}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Delivery Partner Info */}
          {order.logisticProvider === "MP2" && order.logisticDetails?.riderName && (
            <>
              <Separator />
              <div>
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <Truck className="w-4 h-4" />
                  Delivery Partner
                </h3>
                <div className="text-sm space-y-2">
                  <div>
                    <strong>Name:</strong> {order.logisticDetails.riderName}
                  </div>
                  {order.logisticDetails.riderPhone && (
                    <div className="flex items-center gap-2">
                      <strong>Phone:</strong>
                      <span>{order.logisticDetails.riderPhone}</span>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handlePhoneClick(order.logisticDetails!.riderPhone.toString())}
                        className="h-6 w-6 p-0"
                      >
                        <Phone className="w-3 h-3" />
                      </Button>
                    </div>
                  )}
                  {order.logStatus && (
                    <div>
                      <strong>Logistics Status:</strong>
                      <Badge className="ml-2">{getLogisticStatusDisplayName(order.logStatus)}</Badge>
                    </div>
                  )}
                  {order.logisticDetails.trackingUrl && (
                    <div>
                      <strong>Tracking:</strong>
                      <a
                        href={order.logisticDetails.trackingUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="ml-2 text-blue-600 hover:underline"
                      >
                        Track Order
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Order Items */}
          {order.orderItems && order.orderItems.length > 0 && (
            <>
              <Separator />
              <div>
                <h3 className="font-semibold mb-3">Order Items</h3>
                <div className="space-y-3">
                  {order.orderItems.map((item) => (
                    <div key={item.inventoryId} className="border rounded p-3 bg-gray-50">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="font-medium">{item.itemName}</div>
                          {item.description && <div className="text-sm text-gray-600">{item.description}</div>}
                          {item.itemVariationList && item.itemVariationList.length > 0 && (
                            <div className="text-sm text-gray-600">
                              Variations: {item.itemVariationList.map((v) => `${v.name} (+₹${v.price})`).join(", ")}
                            </div>
                          )}
                          {item.aogList && item.aogList.length > 0 && (
                            <div className="text-sm text-gray-600">
                              Add-ons:{" "}
                              {item.aogList
                                .flatMap((aog) => aog.addOnItemList.map((addon) => `${addon.name} (+₹${addon.price})`))
                                .join(", ")}
                            </div>
                          )}
                          <div className="text-xs text-gray-500 mt-1">
                            {item.diet && (
                              <span
                                className={`inline-block w-2 h-2 rounded-full mr-1 ${item.diet === "veg"
                                  ? "bg-green-500"
                                  : item.diet === "nonveg"
                                    ? "bg-red-500"
                                    : "bg-yellow-500"
                                  }`}
                              />
                            )}
                            {item.unit} • {item.packaging}
                          </div>
                        </div>
                        <div className="text-right ml-4">
                          <div className="text-sm">Qty: {item.orderedQty}</div>
                          <div className="text-sm">
                            ₹{item.pricePerUnit} per {item.unit}
                          </div>
                          <div className="font-semibold">₹{item.orderedAmount}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          <Separator />

          {/* Payment Breakdown */}
          <div>
            <h3 className="font-semibold mb-3">Payment Breakdown</h3>
            <div className="bg-gray-50 rounded p-4 space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Item Total:</span>
                <span>₹{order.itemsTotalAmount}</span>
              </div>
              <div className="flex justify-between">
                <span>Delivery Charge:</span>
                <span>₹{order.totalDeliveryCharge}</span>
              </div>
              <div className="flex justify-between">
                <span>Packaging Charges:</span>
                <span>₹{order.packagingCharges}</span>
              </div>
              <div className="flex justify-between">
                <span>Platform Fee:</span>
                <span>₹{order.platformFee}</span>
              </div>
              <div className="flex justify-between">
                <span>Tax Amount:</span>
                <span>₹{order.totalTaxAmount}</span>
              </div>
              <div className="flex justify-between text-red-600">
                <span>Discount:</span>
                <span>-₹{order.totalDiscountAmount}</span>
              </div>
              <Separator />
              <div className="flex justify-between font-semibold text-lg">
                <span>Total Amount:</span>
                <span>₹{order.totalOrderGroupAmount}</span>
              </div>
              <div className="flex justify-between text-sm text-gray-600">
                <span>COD Amount:</span>
                <span>₹{order.codAmount}</span>
              </div>
              {order.walletAmount > 0 && (
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Wallet Amount:</span>
                  <span>₹{order.walletAmount}</span>
                </div>
              )}
            </div>
          </div>

          {/* Support Tickets */}
          {order.supportTickets && order.supportTickets.length > 0 && (
            <>
              <Separator />
              <div>
                <h3 className="font-semibold mb-3">Support Tickets</h3>
                <div className="space-y-3">
                  {order.supportTickets.map((ticket) => (
                    <div key={ticket.ticketId} className="border rounded p-3">
                      <div className="flex justify-between items-start mb-2">
                        <div className="font-medium">Ticket #{ticket.ticketId}</div>
                        <Badge
                          variant={
                            ticket.status === "OPEN" ? "destructive" : ticket.status === "WIP" ? "default" : "secondary"
                          }
                        >
                          {ticket.status}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600 mb-2">{ticket.description}</div>
                      <div className="text-xs text-gray-500">
                        Created: {new Date(ticket.createdAt).toLocaleString()}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            {order.orderStatus === "Created" && (
              <Button variant="destructive" onClick={() => onAction(order, "cancel")}>
                Cancel Order
              </Button>
            )}
            {(order.orderStatus === "Accepted" || order.orderStatus === "Packed") && order.logisticProvider === "MP2" && (
              <Button variant="default" onClick={() => onAction(order, "manual_fulfillment")}>
                Switch to Manual Fulfillment
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}


// Action Modal
interface ActionModalProps {
  order: rNETOrder | null
  actionType: string
  onClose: () => void
}

export function ActionModal({ order, actionType, onClose }: ActionModalProps) {
  const [formData, setFormData] = useState({
    deliveryPartnerName: "",
    deliveryPartnerPhone: "",
    deliveryCharges: "",
    pickupETA: "",
    deliveryETA: "",
    reason: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  if (!order || !actionType) return null

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      console.log("actionType::", actionType)
      if (actionType === "cancel") {

      } else if (actionType === "manual_fulfillment") {

      }
    } catch (error) {
      console.error("Action failed:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderCancelModal = () => (
    <div className="space-y-4">
      <div className="flex items-center gap-2 p-4 bg-red-50 border border-red-200 rounded">
        <AlertTriangle className="w-5 h-5 text-red-600" />
        <div>
          <p className="font-medium text-red-800">Cancel Order</p>
          <p className="text-sm text-red-600">
            This action is irreversible. The order will be cancelled from all systems.
          </p>
        </div>
      </div>

      <div>
        <Label htmlFor="reason">Cancellation Reason</Label>
        <Textarea
          id="reason"
          value={formData.reason}
          onChange={(e) => setFormData((prev) => ({ ...prev, reason: e.target.value }))}
          placeholder="Enter reason for cancellation..."
          className="mt-1 w-72 block border-2 rounded-md"
        />
      </div>

      <div className="flex gap-2 pt-4">
        <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button variant="destructive" onClick={handleSubmit} disabled={isSubmitting}>
          {isSubmitting ? "Cancelling..." : "Confirm Cancellation"}
        </Button>
      </div>
    </div>
  )

  const renderManualFulfillmentModal = () => (
    <div className="space-y-4">
      <div className="flex items-center gap-2 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <AlertTriangle className="w-5 h-5 text-yellow-600" />
        <div>
          <p className="font-medium text-yellow-800">Switch to Manual Fulfillment</p>
          <p className="text-sm text-yellow-600">This will cancel the MP2 order and switch to manual delivery.</p>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="partnerName">Delivery Partner Name *</Label>
          <Input
            id="partnerName"
            value={formData.deliveryPartnerName}
            onChange={(e) => setFormData((prev) => ({ ...prev, deliveryPartnerName: e.target.value }))}
            placeholder="Enter partner name"
            required
          />
        </div>

        <div>
          <Label htmlFor="partnerPhone">Delivery Partner Phone *</Label>
          <Input
            id="partnerPhone"
            value={formData.deliveryPartnerPhone}
            onChange={(e) => setFormData((prev) => ({ ...prev, deliveryPartnerPhone: e.target.value }))}
            placeholder="Enter phone number"
            required
          />
        </div>

        <div>
          <Label htmlFor="charges">Delivery Charges</Label>
          <Input
            id="charges"
            type="number"
            value={formData.deliveryCharges}
            onChange={(e) => setFormData((prev) => ({ ...prev, deliveryCharges: e.target.value }))}
            placeholder="Enter charges"
          />
        </div>

        <div>
          <Label htmlFor="pickupETA">Pickup ETA</Label>
          <Input
            id="pickupETA"
            type="datetime-local"
            value={formData.pickupETA}
            onChange={(e) => setFormData((prev) => ({ ...prev, pickupETA: e.target.value }))}
          />
        </div>

        <div className="sm:col-span-2">
          <Label htmlFor="deliveryETA">Delivery ETA</Label>
          <Input
            id="deliveryETA"
            type="datetime-local"
            value={formData.deliveryETA}
            onChange={(e) => setFormData((prev) => ({ ...prev, deliveryETA: e.target.value }))}
          />
        </div>
      </div>

      <div className="flex gap-2 pt-4">
        <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting || !formData.deliveryPartnerName || !formData.deliveryPartnerPhone}
        >
          {isSubmitting ? "Switching..." : "Confirm Switch"}
        </Button>
      </div>
    </div>
  )

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{actionType === "cancel" ? "Cancel Order" : "Switch to Manual Fulfillment"}</DialogTitle>
        </DialogHeader>

        {actionType === "cancel" ? renderCancelModal() : renderManualFulfillmentModal()}
      </DialogContent>
    </Dialog>
  )
}


export const getTimeDifferenceInSeconds = (orderTime: string, endTime?: string): number => {
  const orderDate = new Date(orderTime)
  const compareDate = endTime ? new Date(endTime) : new Date()
  return Math.floor((compareDate.getTime() - orderDate.getTime()) / 1000)
}


export const formatTimeElapsed = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) {
    return `${days}d ${hours % 24}h`
  }
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`
  }
  return `${minutes}m`
}


export const getOrderCardStyling = (order: rNETOrder) => {
  const timeDiff = getTimeDifferenceInSeconds(order.createdAt)
  const baseClasses = "border rounded-lg transition-all duration-300 "
  let pulseClasses = ""
  let bgClasses = "bg-white hover:shadow-md "
  let helperText = ""

  // Status-based logic according to PRD
  switch (order.orderStatus) {
    case "Created":
      if (timeDiff > 150) {
        bgClasses = "bg-red-50 hover:bg-red-100 border-red-200 "
        pulseClasses = "card-alert-animated "
        helperText = "Order not yet accepted. Please inform the seller to either accept or cancel the order."
      } else if (timeDiff > 120) {
        bgClasses = "bg-yellow-50 hover:bg-yellow-100 border-yellow-200 "
        pulseClasses = "card-alert-animated "
        helperText = "Order yet to be accepted. Please inform the seller again."
      }
      break

    case "Accepted":
      if (order.logisticProvider === "MP2") {
        if (
          timeDiff > 600 &&
          order.logStatus &&
          ["LOG_CREATED", "LOG_PENDING", "LOG_SEARCHING_AGENT"].includes(order.logStatus)
        ) {
          bgClasses = "bg-red-50 hover:bg-red-100 border-red-200 "
          pulseClasses = "card-alert-animated "
          helperText = "Please highlight the order with mp2 team or start manual fulfilment of the order."
        } else if (
          timeDiff > 360 &&
          order.logStatus &&
          ["LOG_CREATED", "LOG_PENDING", "LOG_SEARCHING_AGENT"].includes(order.logStatus)
        ) {
          bgClasses = "bg-orange-50 hover:bg-orange-100 border-orange-200 "
          pulseClasses = "card-alert-animated "
          helperText = "Please highlight the order to mp2 team and start preparing for manual deliveries."
        } else if (timeDiff >= 600 && order.logStatus === "LOG_AGENT_ASSIGNED") {
          pulseClasses = "card-alert-animated "
          helperText = "Please followup with the rider and check if he is moving toward the restaurant."
        }
      } else if (order.logisticProvider === "SELF" && timeDiff > 900) {
        pulseClasses = "card-alert-animated"
        helperText = "Please follow up with the seller for self delivery."
      }
      break

    case "Packed":
      if (order.logisticProvider === "MP2") {
        if (timeDiff > 1200 && ["LOG_CREATED", "LOG_PENDING", "LOG_SEARCHING_AGENT"].includes(order.logStatus)) {
          bgClasses = "bg-red-50 hover:bg-red-100 border-red-200 "
          pulseClasses = "card-alert-animated"
          helperText = "Please check with the delivery partner and raise the issue with the mp2 team."
        } else if (timeDiff > 1200 && order.logStatus === "LOG_AGENT_ASSIGNED") {
          bgClasses = "bg-red-50 hover:bg-red-100 border-red-200 "
          pulseClasses = "card-alert-animated"
          helperText = "Please follow up with the rider and check if he is moving toward the restaurant."
        }
      } else if (order.logisticProvider === "SELF" && timeDiff > 1200) {
        pulseClasses = "card-alert-animated"
        helperText = "Please follow up with the seller for self delivery."
      }
      break
  }

  return {
    className: baseClasses + bgClasses + pulseClasses,
    helperText,
  }
}


export const getStatusDisplayName = (status: string): string => {
  switch (status) {
    case "Created":
      return "Acceptance Pending"
    case "Packed":
      return "Ready for Pickup"
    default:
      return status
  }
}


export const getLogisticStatusDisplayName = (logStatus: string): string => {
  return logStatus
    .replace("LOG_", "")
    .replace("_", " ")
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ")
}


export const mockOrders: rNETOrder[] = [
  {
    // Basic order info
    orderGroupId: 12345,
    orderStatus: "Created",
    logStatus: "LOG_CREATED",
    createdAt: new Date(Date.now() - 3 * 60 * 1000).toISOString(), // 3 minutes ago
    updatedAt: new Date(Date.now() - 2 * 60 * 1000).toISOString(),

    // Business Info
    sellerId: 501,
    sellerName: "Pizza Palace",
    sellerMobile: "9876543211",
    buyerId: 301,
    buyerName: "John Smith",
    buyerMobile: "9876543210",
    nBuyerId: 401,
    networkId: 201,
    networkName: "Local Network",
    agentUserId: 601,
    agentName: "Agent Smith",
    sellerInventoryId: 701,

    // Delivery info
    deliveryDate: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
    deliveryType: "DELIVERY",
    deliveryOtp: "1234",
    pickupOtp: "5678",
    delAssignedTo: 801,
    deliveredById: 0,
    deliveredTime: null,

    // Timeline
    acceptedTime: null,
    packedTime: null,
    assignedTime: null,
    pickedUpTime: null,
    deliveryStartTime: null,

    // Financial Info
    itemsTotalAmount: 429,
    totalOrderGroupAmount: 445,
    totalDeliveryCharge: 30,
    codAmount: 445,
    walletAmount: 0,
    totalCreditAmount: 0,
    creditPendingAmount: 0,
    creditCollectedOfflineAmount: 0,
    isCreditUsed: false,
    creditCollectedOffline: false,
    creditCollectedTime: null,
    paymentCompleted: false,

    // Tax & GST Info
    gstTotalAmount: 21,
    igstApplicable: true,
    totalTaxAmount: 21,
    itemsTaxAmount: 18,
    packagingTaxAmount: 1,
    platformTaxAmount: 1,
    deliveryTaxAmount: 1,

    // Platform Charges
    packagingCharges: 10,
    platformFee: 5,
    pcBasic: 20,
    pcAgent: 10,
    pcTotalWithGst: 35,
    pcApplied: true,
    agentComm: 25,
    salesComm: 15,

    // Item & Quantity Info
    totalItems: 2,
    totalQty: 3,
    weight: 1.5,
    totalBoxes: 1,
    totalBags: 0,
    pickedItemsCount: 0,
    pickedItemsWeight: 0,
    deliveredItems: 0,
    cancelledItems: 0,

    // Cancelled & Returned Info
    cancelledWeight: 0,
    cancelledAmount: 0,
    dispatchedWeight: 0,
    dispatchedAmount: 0,
    returnedWeight: 0,
    returnedAmount: 0,

    // Discount & Coupon Info
    totalDiscountAmount: 50,
    totalItemsStrikeoffAmount: 10,
    discountId: 1501,
    couponId: 1601,
    couponCode: "SAVE10",
    couponDiscountAmount: 40,

    // Address Info
    bAreaId: 2001,
    bAreaName: "Sector 15",
    bAddress: "123 Main St, Sector 15, Gurgaon",
    bAddressName: "Home",
    geoLocationLat: 28.4595,
    geoLocationLng: 77.0266,

    // Trip Info
    tripId: 0,
    tripSeqNumber: 0,

    // Additional Info
    ondcDomain: "RET11",
    fulfillmentType: "DELIVERY",
    sellerMessage: "Fresh pizza made with care",
    preconfirmUid: "PRE123456",
    supplierItemAmount: 400,
    supplierItemWeight: 1.2,

    // Logistics Info
    logisticProvider: "MP2",
    logisticDetails: {
      id: 4001,
      createdAt: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 1 * 60 * 1000).toISOString(),
      orderGroupId: 12345,
      mp2OrderId: "MP2_ORDER_789456",
      clientOrderId: "CLIENT_123456",
      orderState: "Order-created",
      lspId: "LSP_001",
      lspName: "FastDelivery Logistics",
      lspItemId: "ITEM_001",
      lspNetworkOrderId: "NET_ORDER_456",
      riderName: "Raj Kumar",
      riderPhone: "**********",
      riderLastLat: 28.4605,
      riderLastLng: 77.0276,
      price: 30,
      distance: 5.2,
      estDeliveryCharges: 30,
      estDeliveryTime: 30,
      mp2CreatedAt: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
      assignedAt: null,
      pickedupAt: null,
      deliveredAt: null,
      cancelledAt: null,
      rtoInitiatedAt: null,
      rtoDeliveredAt: null,
      estimatedPickupTime: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
      pickupProof: "",
      deliveryProof: "",
      trackingUrl: "https://mp2.example.com/track/MP2_ORDER_789456",
      cancellationReasonId: 0,
      cancellationReasonDesc: "",
      cancelledBy: 0,
      notes: "Handle with care",
      note2: "",
      lNote: "",
      estimateId: "EST_123456",
      lHandlingProvider: "MP2",
      lHandlingProviderUpdatedAt: null,
      lRiderName: "",
      lRiderPhone: 0,
      lPrice: 0,
      lPotp: 0,
      lDotp: 0,
    },

    // Support tickets
    supportTickets: [
      {
        ticketId: 1001,
        status: "OPEN",
        description: "Customer wants to change delivery address",
        createdAt: new Date().toISOString(),
      },
    ],

    // Order items
    orderItems: [
      {
        inventoryId: 1001,
        sellerItemId: 2001,
        masterItemId: 3001,
        itemName: "Margherita Pizza",
        itemUrl: "",
        packaging: "Box",
        itemTag: "pizza",
        unit: "piece",
        pricePerUnit: 299,
        incrementOrderQty: 1,
        minimumOrderQty: 1,
        maxAvailableQty: 10,
        orderedQty: 1,
        orderedAmount: 349,
        soldout: false,
        closed: false,
        unitWtFactor: 1,
        itemCategories: [1, 2],
        strikeoffPrice: 399,
        discPerc: 10,
        itemRegionalLanguageName: "मार्गेरिटा पिज्जा",
        newlyAdded: false,
        freqScore: 8.5,
        varGroup: "size",
        varSeq: 1,
        diet: "veg",
        description: "Classic margherita pizza with fresh basil",
        freeItem: false,
        supplier: "Pizza Palace Kitchen",
        itemVariationList: [
          {
            id: 1,
            name: "Large",
            groupName: "Size",
            seq: 1,
            price: 50,
            strikeoffPrice: 70,
            qty: 1,
            addOnGroupList: [],
          },
        ],
        aogList: [
          {
            id: 1,
            minSelect: 0,
            maxSelect: 3,
            name: "Extra Toppings",
            description: "Add extra toppings",
            seq: 1,
            addOnItemList: [
              {
                id: "addon1",
                sId: "s1",
                name: "Extra Cheese",
                price: 30,
                seq: 1,
                qty: 1,
                diet: "veg",
              },
              {
                id: "addon2",
                sId: "s2",
                name: "Mushrooms",
                price: 25,
                seq: 2,
                qty: 1,
                diet: "veg",
              },
            ],
          },
        ],
      },
      {
        inventoryId: 1002,
        sellerItemId: 2002,
        masterItemId: 3002,
        itemName: "Coke",
        itemUrl: "",
        packaging: "Bottle",
        itemTag: "beverage",
        unit: "bottle",
        pricePerUnit: 40,
        incrementOrderQty: 1,
        minimumOrderQty: 1,
        maxAvailableQty: 20,
        orderedQty: 2,
        orderedAmount: 80,
        soldout: false,
        closed: false,
        unitWtFactor: 0.5,
        itemCategories: [3],
        strikeoffPrice: 50,
        discPerc: 0,
        itemRegionalLanguageName: "कोक",
        newlyAdded: false,
        freqScore: 9.0,
        varGroup: "",
        varSeq: 0,
        diet: "veg",
        description: "Chilled Coca-Cola",
        freeItem: false,
        supplier: "Beverage Supplier",
      },
    ],
  },
  {
    // Second order - Accepted status
    orderGroupId: 12346,
    orderStatus: "Accepted",
    logStatus: "LOG_AGENT_ASSIGNED",
    createdAt: new Date(Date.now() - 8 * 60 * 1000).toISOString(), // 8 minutes ago
    updatedAt: new Date(Date.now() - 3 * 60 * 1000).toISOString(),

    // Business Info
    sellerId: 502,
    sellerName: "Burger Junction",
    sellerMobile: "9876543221",
    buyerId: 302,
    buyerName: "Priya Sharma",
    buyerMobile: "9876543220",
    nBuyerId: 402,
    networkId: 202,
    networkName: "Food Network",
    agentUserId: 602,
    agentName: "Agent Jones",
    sellerInventoryId: 702,

    // Delivery info
    deliveryDate: new Date(Date.now() + 1 * 60 * 60 * 1000).toISOString(),
    deliveryType: "DELIVERY",
    deliveryOtp: "5678",
    pickupOtp: "9012",
    delAssignedTo: 802,
    deliveredById: 0,
    deliveredTime: null,

    // Timeline
    acceptedTime: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
    packedTime: null,
    assignedTime: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
    pickedUpTime: null,
    deliveryStartTime: null,

    // Financial Info
    itemsTotalAmount: 520,
    totalOrderGroupAmount: 569,
    totalDeliveryCharge: 0,
    codAmount: 569,
    walletAmount: 0,
    totalCreditAmount: 0,
    creditPendingAmount: 0,
    creditCollectedOfflineAmount: 0,
    isCreditUsed: false,
    creditCollectedOffline: false,
    creditCollectedTime: null,
    paymentCompleted: false,

    // Tax & GST Info
    gstTotalAmount: 26,
    igstApplicable: true,
    totalTaxAmount: 26,
    itemsTaxAmount: 22,
    packagingTaxAmount: 2,
    platformTaxAmount: 1,
    deliveryTaxAmount: 1,

    // Platform Charges
    packagingCharges: 15,
    platformFee: 8,
    pcBasic: 25,
    pcAgent: 12,
    pcTotalWithGst: 42,
    pcApplied: true,
    agentComm: 30,
    salesComm: 18,

    // Item & Quantity Info
    totalItems: 2,
    totalQty: 3,
    weight: 0.8,
    totalBoxes: 1,
    totalBags: 1,
    pickedItemsCount: 0,
    pickedItemsWeight: 0,
    deliveredItems: 0,
    cancelledItems: 0,

    // Cancelled & Returned Info
    cancelledWeight: 0,
    cancelledAmount: 0,
    dispatchedWeight: 0,
    dispatchedAmount: 0,
    returnedWeight: 0,
    returnedAmount: 0,

    // Discount & Coupon Info
    totalDiscountAmount: 0,
    totalItemsStrikeoffAmount: 0,
    discountId: 0,
    couponId: 0,
    couponCode: "",
    couponDiscountAmount: 0,

    // Address Info
    bAreaId: 2002,
    bAreaName: "Sector 22",
    bAddress: "456 Park Avenue, Sector 22, Gurgaon",
    bAddressName: "Office",
    geoLocationLat: 28.4695,
    geoLocationLng: 77.0366,

    // Trip Info
    tripId: 0,
    tripSeqNumber: 0,

    // Additional Info
    ondcDomain: "RET11",
    fulfillmentType: "DELIVERY",
    sellerMessage: "Fresh burgers prepared",
    preconfirmUid: "PRE123457",
    supplierItemAmount: 500,
    supplierItemWeight: 0.7,

    // Logistics Info
    logisticProvider: "MP2",
    logisticDetails: {
      id: 4002,
      createdAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
      orderGroupId: 12346,
      mp2OrderId: "MP2_ORDER_789457",
      clientOrderId: "CLIENT_123457",
      orderState: "Agent-assigned",
      lspId: "LSP_002",
      lspName: "Quick Delivery",
      lspItemId: "ITEM_002",
      lspNetworkOrderId: "NET_ORDER_457",
      riderName: "Amit Singh",
      riderPhone: "**********",
      riderLastLat: 28.4705,
      riderLastLng: 77.0376,
      price: 0,
      distance: 3.8,
      estDeliveryCharges: 0,
      estDeliveryTime: 25,
      mp2CreatedAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      assignedAt: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
      pickedupAt: null,
      deliveredAt: null,
      cancelledAt: null,
      rtoInitiatedAt: null,
      rtoDeliveredAt: null,
      estimatedPickupTime: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
      pickupProof: "",
      deliveryProof: "",
      trackingUrl: "https://mp2.example.com/track/MP2_ORDER_789457",
      cancellationReasonId: 0,
      cancellationReasonDesc: "",
      cancelledBy: 0,
      notes: "Self delivery preferred",
      note2: "",
      lNote: "",
      estimateId: "EST_123457",
      lHandlingProvider: "MP2",
      lHandlingProviderUpdatedAt: null,
      lRiderName: "",
      lRiderPhone: 0,
      lPrice: 0,
      lPotp: 0,
      lDotp: 0,
    },

    // Support tickets
    supportTickets: [],

    // Order items
    orderItems: [
      {
        inventoryId: 1003,
        sellerItemId: 2003,
        masterItemId: 3003,
        itemName: "Chicken Burger",
        itemUrl: "",
        packaging: "Box",
        itemTag: "burger",
        unit: "piece",
        pricePerUnit: 180,
        incrementOrderQty: 1,
        minimumOrderQty: 1,
        maxAvailableQty: 15,
        orderedQty: 2,
        orderedAmount: 360,
        soldout: false,
        closed: false,
        unitWtFactor: 0.3,
        itemCategories: [4, 5],
        strikeoffPrice: 220,
        discPerc: 0,
        itemRegionalLanguageName: "चिकन बर्गर",
        newlyAdded: false,
        freqScore: 7.8,
        varGroup: "",
        varSeq: 0,
        diet: "nonveg",
        description: "Juicy chicken burger with fresh vegetables",
        freeItem: false,
        supplier: "Burger Junction Kitchen",
        aogList: [
          {
            id: 2,
            minSelect: 0,
            maxSelect: 2,
            name: "Extra Patty",
            description: "Add extra chicken patty",
            seq: 1,
            addOnItemList: [
              {
                id: "addon3",
                sId: "s3",
                name: "Extra Patty",
                price: 40,
                seq: 1,
                qty: 1,
                diet: "nonveg",
              },
            ],
          },
        ],
      },
      {
        inventoryId: 1004,
        sellerItemId: 2004,
        masterItemId: 3004,
        itemName: "French Fries",
        itemUrl: "",
        packaging: "Box",
        itemTag: "sides",
        unit: "serving",
        pricePerUnit: 80,
        incrementOrderQty: 1,
        minimumOrderQty: 1,
        maxAvailableQty: 25,
        orderedQty: 1,
        orderedAmount: 80,
        soldout: false,
        closed: false,
        unitWtFactor: 0.2,
        itemCategories: [6],
        strikeoffPrice: 100,
        discPerc: 0,
        itemRegionalLanguageName: "फ्रेंच फ्राइज़",
        newlyAdded: false,
        freqScore: 8.2,
        varGroup: "",
        varSeq: 0,
        diet: "veg",
        description: "Crispy golden french fries",
        freeItem: false,
        supplier: "Burger Junction Kitchen",
      },
    ],
  },
  {
    // Third order - Delivered status
    orderGroupId: 12347,
    orderStatus: "Delivered",
    logStatus: "LOG_DELIVERED",
    createdAt: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // 45 minutes ago
    updatedAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(),

    // Business Info
    sellerId: 503,
    sellerName: "Spice Garden",
    sellerMobile: "9876543241",
    buyerId: 303,
    buyerName: "Sneha Patel",
    buyerMobile: "9876543240",
    nBuyerId: 403,
    networkId: 203,
    networkName: "Spice Network",
    agentUserId: 603,
    agentName: "Agent Brown",
    sellerInventoryId: 703,

    // Delivery info
    deliveryDate: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
    deliveryType: "DELIVERY",
    deliveryOtp: "3456",
    pickupOtp: "7890",
    delAssignedTo: 803,
    deliveredById: 803,
    deliveredTime: new Date(Date.now() - 5 * 60 * 1000).toISOString(),

    // Timeline
    acceptedTime: new Date(Date.now() - 40 * 60 * 1000).toISOString(),
    packedTime: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    assignedTime: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
    pickedUpTime: new Date(Date.now() - 20 * 60 * 1000).toISOString(),
    deliveryStartTime: new Date(Date.now() - 15 * 60 * 1000).toISOString(),

    // Financial Info
    itemsTotalAmount: 560,
    totalOrderGroupAmount: 623,
    totalDeliveryCharge: 40,
    codAmount: 623,
    walletAmount: 0,
    totalCreditAmount: 0,
    creditPendingAmount: 0,
    creditCollectedOfflineAmount: 0,
    isCreditUsed: false,
    creditCollectedOffline: false,
    creditCollectedTime: null,
    paymentCompleted: true,

    // Tax & GST Info
    gstTotalAmount: 28,
    igstApplicable: true,
    totalTaxAmount: 28,
    itemsTaxAmount: 24,
    packagingTaxAmount: 2,
    platformTaxAmount: 1,
    deliveryTaxAmount: 1,

    // Platform Charges
    packagingCharges: 15,
    platformFee: 10,
    pcBasic: 30,
    pcAgent: 15,
    pcTotalWithGst: 50,
    pcApplied: true,
    agentComm: 35,
    salesComm: 20,

    // Item & Quantity Info
    totalItems: 3,
    totalQty: 4,
    weight: 1.2,
    totalBoxes: 2,
    totalBags: 0,
    pickedItemsCount: 3,
    pickedItemsWeight: 1.2,
    deliveredItems: 3,
    cancelledItems: 0,

    // Cancelled & Returned Info
    cancelledWeight: 0,
    cancelledAmount: 0,
    dispatchedWeight: 1.2,
    dispatchedAmount: 623,
    returnedWeight: 0,
    returnedAmount: 0,

    // Discount & Coupon Info
    totalDiscountAmount: 30,
    totalItemsStrikeoffAmount: 15,
    discountId: 1502,
    couponId: 1602,
    couponCode: "SPICE20",
    couponDiscountAmount: 15,

    // Address Info
    bAreaId: 2003,
    bAreaName: "Sector 10",
    bAddress: "321 Residential Complex, Sector 10, Gurgaon",
    bAddressName: "Home",
    geoLocationLat: 28.4895,
    geoLocationLng: 77.0566,

    // Trip Info
    tripId: 3001,
    tripSeqNumber: 1,

    // Additional Info
    ondcDomain: "RET11",
    fulfillmentType: "DELIVERY",
    sellerMessage: "Authentic Indian cuisine",
    preconfirmUid: "PRE123458",
    supplierItemAmount: 540,
    supplierItemWeight: 1.1,

    // Logistics Info
    logisticProvider: "MP2",
    logisticDetails: {
      id: 4003,
      createdAt: new Date(Date.now() - 40 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      orderGroupId: 12347,
      mp2OrderId: "MP2_ORDER_789458",
      clientOrderId: "CLIENT_123458",
      orderState: "Order-delivered",
      lspId: "LSP_003",
      lspName: "Express Delivery",
      lspItemId: "ITEM_003",
      lspNetworkOrderId: "NET_ORDER_458",
      riderName: "Vikash Kumar",
      riderPhone: "**********",
      riderLastLat: 28.4905,
      riderLastLng: 77.0576,
      price: 40,
      distance: 6.5,
      estDeliveryCharges: 40,
      estDeliveryTime: 35,
      mp2CreatedAt: new Date(Date.now() - 40 * 60 * 1000).toISOString(),
      assignedAt: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
      pickedupAt: new Date(Date.now() - 20 * 60 * 1000).toISOString(),
      deliveredAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      cancelledAt: null,
      rtoInitiatedAt: null,
      rtoDeliveredAt: null,
      estimatedPickupTime: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      pickupProof: "https://storage.example.com/pickup_proof_123.jpg",
      deliveryProof: "https://storage.example.com/delivery_proof_456.jpg",
      trackingUrl: "https://mp2.example.com/track/MP2_ORDER_789458",
      cancellationReasonId: 0,
      cancellationReasonDesc: "",
      cancelledBy: 0,
      notes: "Delivered successfully",
      note2: "Customer satisfied",
      lNote: "No issues",
      estimateId: "EST_123458",
      lHandlingProvider: "MP2",
      lHandlingProviderUpdatedAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      lRiderName: "",
      lRiderPhone: 0,
      lPrice: 0,
      lPotp: 0,
      lDotp: 0,
    },

    // Support tickets
    supportTickets: [],

    // Order items
    orderItems: [
      {
        inventoryId: 1005,
        sellerItemId: 2005,
        masterItemId: 3005,
        itemName: "Butter Chicken",
        itemUrl: "",
        packaging: "Container",
        itemTag: "curry",
        unit: "serving",
        pricePerUnit: 320,
        incrementOrderQty: 1,
        minimumOrderQty: 1,
        maxAvailableQty: 10,
        orderedQty: 1,
        orderedAmount: 320,
        soldout: false,
        closed: false,
        unitWtFactor: 0.4,
        itemCategories: [7, 8],
        strikeoffPrice: 380,
        discPerc: 5,
        itemRegionalLanguageName: "बटर चिकन",
        newlyAdded: false,
        freqScore: 9.2,
        varGroup: "",
        varSeq: 0,
        diet: "nonveg",
        description: "Rich and creamy butter chicken curry",
        freeItem: false,
        supplier: "Spice Garden Kitchen",
      },
      {
        inventoryId: 1006,
        sellerItemId: 2006,
        masterItemId: 3006,
        itemName: "Naan",
        itemUrl: "",
        packaging: "Wrapper",
        itemTag: "bread",
        unit: "piece",
        pricePerUnit: 45,
        incrementOrderQty: 1,
        minimumOrderQty: 1,
        maxAvailableQty: 20,
        orderedQty: 2,
        orderedAmount: 90,
        soldout: false,
        closed: false,
        unitWtFactor: 0.1,
        itemCategories: [9],
        strikeoffPrice: 55,
        discPerc: 0,
        itemRegionalLanguageName: "नान",
        newlyAdded: false,
        freqScore: 8.8,
        varGroup: "",
        varSeq: 0,
        diet: "veg",
        description: "Soft and fluffy Indian bread",
        freeItem: false,
        supplier: "Spice Garden Kitchen",
      },
      {
        inventoryId: 1007,
        sellerItemId: 2007,
        masterItemId: 3007,
        itemName: "Basmati Rice",
        itemUrl: "",
        packaging: "Container",
        itemTag: "rice",
        unit: "serving",
        pricePerUnit: 150,
        incrementOrderQty: 1,
        minimumOrderQty: 1,
        maxAvailableQty: 15,
        orderedQty: 1,
        orderedAmount: 150,
        soldout: false,
        closed: false,
        unitWtFactor: 0.3,
        itemCategories: [10],
        strikeoffPrice: 180,
        discPerc: 0,
        itemRegionalLanguageName: "बासमती चावल",
        newlyAdded: false,
        freqScore: 8.5,
        varGroup: "",
        varSeq: 0,
        diet: "veg",
        description: "Aromatic basmati rice",
        freeItem: false,
        supplier: "Spice Garden Kitchen",
      },
    ],
  },
]
