import { Edit, Save, X } from "lucide-react";
import { RadioGroup, RadioGroupItem } from "./radio-group";
import { Label } from "./label";
import { SellerConfig } from "~/types/api/businessConsoleService/SellerManagement";
import { useEffect, useState } from "react";
import { Input } from "./input";

interface SellerProps {
      sellerConfig: SellerConfig;
      onAttributeUpdate: (data: Record<string, string>) => Promise<void>;
}

export default function SellerConfigDetails({ sellerConfig, onAttributeUpdate }: SellerProps) {
      const [config, setConfig] = useState<SellerConfig>(sellerConfig);
      const [error, setError] = useState<string | null>(null);

      // Pending state for each section:
      const [isEditingOperation, setIsEditingOperation] = useState(false);
      const [pendingOperation, setPendingOperation] = useState<Partial<SellerConfig>>({});

      const [isEditingDelivery, setIsEditingDelivery] = useState(false);
      const [pendingDelivery, setPendingDelivery] = useState<Partial<SellerConfig>>({});

      const [isEditingBusiness, setIsEditingBusiness] = useState(false);
      const [pendingBusiness, setPendingBusiness] = useState<Partial<SellerConfig>>({});

      const [isEditingPackaging, setIsEditingPackaging] = useState(false);
      const [pendingPackaging, setPendingPackaging] = useState<Partial<SellerConfig>>({});

      const [isEditingInventory, setIsEditingInventory] = useState(false);
      const [pendingInventory, setPendingInventory] = useState<Partial<SellerConfig>>({});

      useEffect(() => {
            if (sellerConfig) {
                  setConfig(sellerConfig);
            }
      }, [sellerConfig]);

      // Map internal keys to camelCase names expected by backend
      const keyMapping: Record<keyof SellerConfig, string> = {
            id: "id",
            name: "name",
            enabled: "enabled",
            auto_accept: "autoAccept",
            auto_pack: "autoPack",
            auto_pickup: "autoPickup",
            auto_dispatch: "autoDispatch",
            listing_seq: "listingSequence",
            mininum_required_balance: "minimumRequiredBalance",
            allow_cod: "allowCoD",
            minimum_order_qty: "minimumOrderQty",
            minimum_order_value: "minimumOrderValue",
            wa_enable: "waEnable",
            approx_pricing: "approxPricing",
            approxPriceVisibility: "approxPriceVisibility",
            strikeoff_enabled: "strikeoffEnabled",
            item_pick_enabled: "itemPickEnabled",
            contract_price_enabled: "contractPriceEnabled",
            delivery_type: "deliveryType",
            fav_items_enabled: "favItemsEnabled",
            category_level: "categoryLevel",
            business_id: "businessId",
            dispatch_time: "dispatchTime",
            delivery_time: "deliveryTime",
            approxDelDateVisibility: "approxDelDateVisibility",
            instantDeliveryTime: "instantDeliveryTime",
            booking_close_time: "bookingCloseTime",
            booking_open_time: "bookingOpenTime",
            is_pay_later_enabled: "isPayLaterEnabled",
            distanceBasedDel: "distanceBasedDel",
            deliveryDistance: "deliveryDistance",
            auto_activate: "autoActivate",
            advance_booking_days: "advanceBookingDays",
            miSources: "miSources",
            t1Open: "t1Open",
            t1Close: "t1Close",
            t2Open: "t2Open",
            t2Close: "t2Close",
            t3Open: "t3Open",
            t3Close: "t3Close",
            menuId: "menuId",
            pos: "pos",
            posCustId: "posCustId",
            posRetName: "posRetName",
            posRetContactNo: "posRetContactNo",
            posRetAddress: "posRetAddress",
            outletId: "outletId",
            ondcDomain: "ondcDomain",
            defaultOrderPrepTime: "defaultOrderPrepTime",
            spCustId: "spCustId",
            spId: "spId",
            sourceSystem: "sourceSystem",
            logisticProvider: "logisticProvider",
            packagingCharge: "packagingCharge",
            packagingChargeType: "packagingChargeType",
            packagingApplicableOn: "packagingApplicableOn",
      };

      const [validationErrors, setValidationErrors] = useState<Partial<Record<keyof SellerConfig, string>>>();

      // Timing Helpers for Booking Timings
      const getDisplayedBookingTime = (bookingTime: number | string): string => {
            console.log("getDisplayedBookingTime Trigger for", bookingTime);
            const dispatch = Number(config.dispatch_time.split(":")[0]);
            let bookingInHours: number;
            if (typeof bookingTime === "string") {
                  const [h, m] = bookingTime.split(":").map(Number);
                  bookingInHours = h + m / 60;
            } else {
                  bookingInHours = bookingTime / 60;
            }
            if (dispatch < bookingInHours) bookingInHours += 24;
            const displayedHours = dispatch - bookingInHours;
            const hours = Math.floor(displayedHours);
            const minutes = Math.round((displayedHours - hours) * 60);
            const displayedString = `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
            console.log("getDisplayedBookingTime return value", displayedString);
            return displayedString;
      };

      const convertDisplayedBookingToBE = (displayed: string): number => {
            const [h, m] = displayed.split(":").map(Number);
            if (isNaN(h) || isNaN(m)) {
                  throw new Error("Invalid displayed time format");
            }
            const inputHours = h + m / 60;
            const dispatch = Number(config.dispatch_time.split(":")[0]);
            let beValue = (dispatch - inputHours) * 60;
            if (beValue < 0) beValue += 24 * 60;
            const fixedBeValue = Math.round(beValue * 100) / 100;
            console.log("convertDisplayedBookingToBE return value", fixedBeValue);
            return fixedBeValue;
      };

      // Helper to update pending state for a section
      const updatePending = (
            section: "operation" | "delivery" | "business" | "packaging" | "inventory",
            key: keyof SellerConfig,
            value: any
      ) => {
            console.log(`updatePending: section=${section}, key=${key}, value=`, value);
            if (section === "operation") {
                  setPendingOperation(prev => ({ ...prev, [key]: value }));
            } else if (section === "delivery") {
                  setPendingDelivery(prev => ({ ...prev, [key]: value }));
            } else if (section === "business") {
                  setPendingBusiness(prev => ({ ...prev, [key]: value }));
            } else if (section === "packaging") {
                  setPendingPackaging(prev => ({ ...prev, [key]: value }));
            } else if (section === "inventory") {
                  setPendingInventory(prev => ({ ...prev, [key]: value }));
            }
      };

      // Field definitions
      const basicFields = [
            { label: "ID", key: "id" },
            { label: "Name", key: "name" },
      ];

      const operationalConfigFields = [
            { label: "Are the orders required to be accepted manually?", key: "auto_accept" },
            { label: "Are the orders required to be packed manually?", key: "auto_pack" },
            { label: "Are the orders required to be picked manually?", key: "auto_pickup" },
            { label: "Are the orders required to be dispatched manually?", key: "auto_dispatch" },
            { label: "Are the items required to be picked individually?", key: "item_pick_enabled" },
      ];

      const deliveryConfigRadioFields = [
            { label: "Is Cash on delivery allowed?", key: "allow_cod" },
            { label: "Is Buy now pay later (credit) allowed?", key: "is_pay_later_enabled" },
            { label: "Distance based delivery restriction:", key: "distanceBasedDel" },
            { label: "Display Approx Delivery Date?", key: "approxDelDateVisibility" },
      ];

      const deliveryConfigOptions = [
            {
                  label: "Delivery Type",
                  key: "delivery_type",
                  options: [
                        { id: "delivery_type-daily", value: "daily", label: "🚚 Daily" },
                        { id: "delivery_type-instant", value: "instant", label: "🏍️ Instant" },
                  ],
            },
            {
                  label: "Point of Sale",
                  key: "pos",
                  options: [
                        { id: "pos-none", value: "none", label: "None" },
                        { id: "pos-petpooja", value: "petpooja", label: "Petpooja" },
                  ],
            },
            {
                  label: "Source System",
                  key: "sourceSystem",
                  options: [
                        { id: "sourceSystem-mnet", value: "MNET", label: "MNET" },
                        { id: "sourceSystem-ondc", value: "ONDC", label: "ONDC" },
                  ],
            },
            {
                  label: "ONDC Domain",
                  key: "ondcDomain",
                  options: [
                        { id: "ondcDomain-ret11", value: "RET11", label: "Restaurants (RET11)" },
                        { id: "ondcDomain-ret10", value: "RET10", label: "Others (RET10)" },
                  ],
            },
      ];

      const businessConfigFields = [
            { label: "Business Listing Sequence:", key: "listing_seq", type: "number", prefix: "" },
            { label: "Minimum Required Balance:", key: "mininum_required_balance", type: "number", prefix: "₹" },
      ];

      const businessConfigRadioFields = [
            { label: "Is WhatsApp Enabled?", key: "wa_enable" },
      ];

      const inventoryConfigRadioFields = [
            { label: "Is the Inventory required to be opened manually?", key: "auto_activate" },
            { label: "Is item pricing approximate?", key: "approx_pricing" },
            { label: "Display Prices?", key: "approxPriceVisibility" },
            { label: "Is item level discounting allowed?", key: "strikeoff_enabled" },
            { label: "Is item x buyer level prices fixed? (Contract Pricing)", key: "contract_price_enabled" },
            { label: "Are previously bought items recommended?", key: "fav_items_enabled" },
      ];

      const inventoryConfigFields = [
            { label: "Minimum Order Qty:", key: "minimum_order_qty", type: "number", prefix: "", suffix: "kg" },
            { label: "Advance Booking duration:", key: "advance_booking_days", type: "number", prefix: "", suffix: "days" },
            { label: "Minimum Order Value:", key: "minimum_order_value", type: "number", prefix: "₹", suffix: "" },
            { label: "MI Sources:", key: "miSources", type: "text", prefix: "", suffix: "" },
            {
                  label: "Logistic Partner:",
                  key: "logisticProvider",
                  type: "select", // Change type to "select" to indicate a dropdown
                  prefix: "",
                  suffix: "",
                  options: [
                        { value: "MP2", label: "MP2" },
                        { value: "SELF", label: "SELF" },
                  ],
            },
      ];
      // Save handler
      const handleSaveSection = async (section: "operation" | "delivery" | "business" | "packaging" | "inventory") => {
            let pending: Partial<SellerConfig> = {};
            if (section === "operation") pending = pendingOperation;
            else if (section === "delivery") pending = pendingDelivery;
            else if (section === "business") pending = pendingBusiness;
            else if (section === "packaging") pending = pendingPackaging;
            else if (section === "inventory") pending = pendingInventory;

            //validations
            let errors: Partial<Record<keyof SellerConfig, string>> = {};
            if (section === "delivery") {
                  if (pending.pos !== undefined && pending.pos === "petpooja") {
                        if (!pending.menuId) errors.menuId = "Menu ID is required";
                        if (!pending.posCustId) errors.posCustId = "POS Customer ID is required";
                        if (!pending.posRetName) errors.posRetName = "POS Restaurant Name is required";
                        if (!pending.posRetContactNo) errors.posRetContactNo = "POS Restaurant Contact Number is required";
                        if (!pending.posRetAddress) errors.posRetAddress = "POS Restaurant Address is required";
                  }
                  if (pending.ondcDomain !== undefined && pending.ondcDomain === "RET11") {
                        if (!pending.defaultOrderPrepTime) errors.defaultOrderPrepTime = "Default Order Preparation Time is required";
                  }
            }
            if (section === "packaging") {
                  if (pending.packagingCharge === undefined || pending.packagingCharge === null || String(pending.packagingCharge) === "") errors.packagingCharge = "Packaging Charge is required";
                  if (!pending.packagingChargeType) errors.packagingChargeType = "Packaging Charge Type is required";
                  if (!pending.packagingApplicableOn) errors.packagingApplicableOn = "Packaging Applicable On is required";
            }
            if (errors && Object.keys(errors).length > 0) {
                  setValidationErrors(errors);
                  return;
            } else {
                  setValidationErrors({});
            }

            // Filter out null or undefined values
            const updateArray = Object.entries(pending)
                  .filter(([_, value]) => value !== null && value !== undefined)
                  .map(([key, value]) => {
                        const convertedValue = typeof value === "boolean" ? (value ? true : false) : value;
                        return { attribute: keyMapping[key as keyof SellerConfig], value: convertedValue };
                  });

            if (updateArray.length === 0) {
                  setError("No changes to save");
                  return;
            }

            console.log("Prepared update array:", updateArray);
            try {
                  await onAttributeUpdate({ updates: JSON.stringify(updateArray) });
                  console.log("handleSaveSection: updates sent", updateArray);
                  setConfig(prev => ({ ...prev, ...pending }));
                  setError(null);

                  if (section === "operation") {
                        setPendingOperation({});
                        setIsEditingOperation(false);
                  } else if (section === "delivery") {
                        setPendingDelivery({});
                        setIsEditingDelivery(false);
                  } else if (section === "business") {
                        setPendingBusiness({});
                        setIsEditingBusiness(false);
                  } else if (section === "packaging") {
                        setPendingPackaging({});
                        setIsEditingPackaging(false);
                  } else if (section === "inventory") {
                        setPendingInventory({});
                        setIsEditingInventory(false);
                  }
            } catch (error) {
                  console.error("Error in handleSaveSection:", error);
                  setError(error instanceof Error ? error.message : "Failed to save changes");
            }
      };

      const handleCancelSection = (section: "operation" | "delivery" | "business" | "packaging" | "inventory") => {
            if (section === "operation") {
                  setPendingOperation({});
                  setIsEditingOperation(false);
            } else if (section === "delivery") {
                  setPendingDelivery({});
                  setIsEditingDelivery(false);
            } else if (section === "business") {
                  setPendingBusiness({});
                  setIsEditingBusiness(false);
            } else if (section === "packaging") {
                  setPendingPackaging({});
                  setIsEditingPackaging(false);
            } else if (section === "inventory") {
                  setPendingInventory({});
                  setIsEditingInventory(false);
            }
            setError(null);
            console.log(`Cancelled editing for ${section}`);
      };

      return (
            <div className="flex flex-col gap-4 border bg-white rounded-xl border-neutral-200 my-4 p-4">
                  {error && (
                        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                              <span className="block sm:inline">{error}</span>
                        </div>
                  )}
                  {/* Basic Configurations */}
                  <div className="flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow">
                        <div className="text-lg font-semibold text-typography-700">Basic Configurations</div>
                        <div className="flex flex-col gap-1">
                              {basicFields.map(({ label, key }) => (
                                    <div key={key} className="text-sm text-typography-300">
                                          {label}: {config[key as keyof SellerConfig]}
                                    </div>
                              ))}
                        </div>
                  </div>

                  {/* Operational Configurations */}
                  <div className="flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow">
                        <div className="flex justify-between">
                              <div className="text-lg font-semibold text-typography-700">Operational Configurations</div>
                              <button
                                    onClick={() => {
                                          if (!isEditingOperation) {
                                                setPendingOperation({
                                                      auto_accept: config.auto_accept,
                                                      auto_pack: config.auto_pack,
                                                      auto_pickup: config.auto_pickup,
                                                      auto_dispatch: config.auto_dispatch,
                                                      item_pick_enabled: config.item_pick_enabled,
                                                });
                                                console.log("Entering edit mode for Operational");
                                          }
                                          setIsEditingOperation(prev => !prev);
                                    }}
                                    className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800"
                              >
                                    {isEditingOperation ? (
                                          <>
                                                <X className="h-4 w-4" /> Cancel
                                          </>
                                    ) : (
                                          <>
                                                <Edit className="h-4 w-4" /> Edit
                                          </>
                                    )}
                              </button>
                        </div>
                        {operationalConfigFields.map(({ label, key }) => {
                              const k = key as keyof SellerConfig;
                              const currentValue = isEditingOperation
                                    ? (pendingOperation[k] !== undefined ? pendingOperation[k] : config[k])
                                    : config[k];
                              return (
                                    <div key={key} className="flex gap-4 items-center text-md">
                                          <Label className="w-[400px] text-md text-typography-400">{label}</Label>
                                          <RadioGroup
                                                value={currentValue ? ["item_pick_enabled"].includes(key) ? "yes" : "no" : ["item_pick_enabled"].includes(key) ? "no" : "yes"}
                                                onValueChange={(val) => {
                                                      if (isEditingOperation) {
                                                            if (k === "item_pick_enabled") {
                                                                  updatePending("operation", "item_pick_enabled", val === "yes");
                                                                  console.log(`Operational onChange: ${key} set to ${val === "yes"}`);
                                                            } else {
                                                                  updatePending("operation", k, val === "no");
                                                                  console.log(`Operational onChange: ${key} set to ${val === "no"}`);
                                                            }
                                                      }
                                                }}
                                                disabled={!isEditingOperation}
                                                className="flex gap-4 items-center text-md font-semibold text-typography-800"
                                          >
                                                <div className="flex items-center gap-2">
                                                      <RadioGroupItem id={`${key}-yes`} value="yes" />
                                                      <Label htmlFor={`${key}-yes`}>Yes</Label>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                      <RadioGroupItem id={`${key}-no`} value="no" />
                                                      <Label htmlFor={`${key}-no`}>No</Label>
                                                </div>
                                          </RadioGroup>
                                    </div>
                              );
                        })}
                        {isEditingOperation && (
                              <div className="flex gap-4 justify-center">
                                    <button onClick={() => handleCancelSection("operation")} className="px-4 py-2 border rounded-md">
                                          Cancel
                                    </button>
                                    <button onClick={() => handleSaveSection("operation")} className="px-6 py-2 bg-primary text-white rounded-md">
                                          Save
                                    </button>
                              </div>
                        )}
                  </div>

                  {/* Delivery Configurations */}
                  <div className="flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow">
                        <div className="flex justify-between">
                              <div className="text-lg font-semibold text-typography-700">Delivery Configurations</div>
                              <button
                                    onClick={() => {
                                          if (!isEditingDelivery) {
                                                setPendingDelivery({
                                                      allow_cod: config.allow_cod,
                                                      is_pay_later_enabled: config.is_pay_later_enabled,
                                                      distanceBasedDel: config.distanceBasedDel,
                                                      deliveryDistance: config.deliveryDistance,
                                                      delivery_type: config.delivery_type,
                                                      dispatch_time: config.dispatch_time,
                                                      delivery_time: config.delivery_time,
                                                      approxDelDateVisibility: config.approxDelDateVisibility,
                                                      instantDeliveryTime: config.instantDeliveryTime,
                                                      booking_open_time: config.booking_open_time,
                                                      booking_close_time: config.booking_close_time,
                                                      t1Open: config.t1Open,
                                                      t1Close: config.t1Close,
                                                      t2Open: config.t2Open,
                                                      t2Close: config.t2Close,
                                                      t3Open: config.t3Open,
                                                      t3Close: config.t3Close,
                                                      pos: config.pos,
                                                      menuId: config.menuId,
                                                      posCustId: config.posCustId,
                                                      posRetName: config.posRetName,
                                                      posRetContactNo: config.posRetContactNo,
                                                      posRetAddress: config.posRetAddress,
                                                      sourceSystem: config.sourceSystem,
                                                      ondcDomain: config.ondcDomain,
                                                      defaultOrderPrepTime: config.defaultOrderPrepTime,
                                                });
                                                console.log("Entering edit mode for Delivery");
                                          }
                                          setIsEditingDelivery(prev => !prev);
                                    }}
                                    className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800"
                              >
                                    {isEditingDelivery ? (
                                          <>
                                                <X className="h-4 w-4" /> Cancel
                                          </>
                                    ) : (
                                          <>
                                                <Edit className="h-4 w-4" /> Edit
                                          </>
                                    )}
                              </button>
                        </div>
                        {deliveryConfigRadioFields.map(({ label, key }) => {
                              const k = key as keyof SellerConfig;
                              const currentValue = isEditingDelivery
                                    ? (pendingDelivery[k] !== undefined ? pendingDelivery[k] : config[k])
                                    : config[k];
                              return (
                                    <>
                                          <div key={key} className="flex gap-4 items-center text-md">
                                                <Label className="w-[400px] text-md text-typography-400">{label}</Label>
                                                <RadioGroup
                                                      value={currentValue ? "yes" : "no"}
                                                      onValueChange={(val) => {
                                                            if (isEditingDelivery) {
                                                                  if (k === "distanceBasedDel" && val === "no") {
                                                                        updatePending("delivery", "deliveryDistance", 0);
                                                                  }
                                                                  updatePending("delivery", k, val === "yes");
                                                                  console.log(`Delivery onChange: ${key} set to ${val === "yes"}`);
                                                            }
                                                      }}
                                                      disabled={!isEditingDelivery}
                                                      className="flex gap-4 items-center text-md font-semibold text-typography-800"
                                                >
                                                      <div className="flex items-center gap-2">
                                                            <RadioGroupItem id={`${key}-yes`} value="yes" />
                                                            <Label htmlFor={`${key}-yes`}>Yes</Label>
                                                      </div>
                                                      <div className="flex items-center gap-2">
                                                            <RadioGroupItem id={`${key}-no`} value="no" />
                                                            <Label htmlFor={`${key}-no`}>No</Label>
                                                      </div>
                                                </RadioGroup>
                                          </div>
                                          {key === "distanceBasedDel" && currentValue === true && (
                                                <div className="flex gap-4 items-center text-md">
                                                      <Label className="w-[400px] text-md text-typography-400">Delivery Distance(km):</Label>
                                                      <input
                                                            type="number"
                                                            min={0}
                                                            value={
                                                                  pendingDelivery.deliveryDistance !== undefined
                                                                        ? pendingDelivery.deliveryDistance ?? ""
                                                                        : config.deliveryDistance ?? ""
                                                            }
                                                            onKeyDown={(e) => {
                                                                  if (e.key === "-" || e.key === "e") {
                                                                        e.preventDefault();
                                                                  }
                                                            }}
                                                            onChange={(e) => {
                                                                  const value = parseFloat(e.target.value);
                                                                  if (value >= 0) {
                                                                        updatePending("delivery", "deliveryDistance", value);
                                                                        console.log(`Delivery Distance set to ${value}`);
                                                                  }
                                                            }}
                                                            disabled={!isEditingDelivery}
                                                            className="border border-neutral-400 rounded-md p-1 px-2"
                                                      />
                                                </div>
                                          )}
                                    </>
                              );
                        })}
                        <div className="flex gap-4 items-center text-md">
                              <Label className="w-[400px] text-md text-typography-400">Instant/Restaurant Delivery time(mins):</Label>
                              <input
                                    type="number"
                                    min={0}
                                    value={
                                          pendingDelivery.instantDeliveryTime !== undefined
                                                ? pendingDelivery.instantDeliveryTime ?? ""
                                                : config.instantDeliveryTime ?? ""
                                    }
                                    onKeyDown={(e) => {
                                          if (e.key === "-" || e.key === "e") {
                                                e.preventDefault();
                                          }
                                    }}
                                    onChange={(e) => {
                                          const value = parseFloat(e.target.value);
                                          updatePending("delivery", "instantDeliveryTime", value);
                                          console.log(`Instant Delivery Time set to ${value}`);

                                    }}
                                    disabled={!isEditingDelivery}
                                    className="border border-neutral-400 rounded-md p-1 px-2"
                              />
                        </div>
                        {deliveryConfigOptions.map(({ label, key, options }) => {
                              const k = key as keyof SellerConfig;
                              const currentValue = isEditingDelivery
                                    ? (pendingDelivery[k] !== undefined ? pendingDelivery[k] : config[k])
                                    : config[k];
                              return (
                                    <>
                                          <div key={key} className="flex gap-4 items-center text-md">
                                                <Label className="w-[400px] text-md text-typography-400">{label}:</Label>
                                                <RadioGroup
                                                      value={currentValue || ""}
                                                      onValueChange={(val) => {
                                                            if (isEditingDelivery) {
                                                                  if (k === "pos" && val === "none") {
                                                                        updatePending("delivery", "menuId", null);
                                                                        updatePending("delivery", "posCustId", null);
                                                                        updatePending("delivery", "posRetName", null);
                                                                        updatePending("delivery", "posRetContactNo", null);
                                                                        updatePending("delivery", "posRetAddress", null);
                                                                  }
                                                                  if (k === "ondcDomain" && val === "RET10") {
                                                                        updatePending("delivery", "defaultOrderPrepTime", null);
                                                                  }
                                                                  updatePending("delivery", k, val);
                                                                  console.log(`Delivery onChange: ${key} set to ${val}`);
                                                            }
                                                      }}
                                                      disabled={!isEditingDelivery}
                                                      className="flex gap-4 items-center text-md font-semibold text-typography-800"
                                                >
                                                      {options.map(({ id, value, label }) => (
                                                            <div key={id} className="flex items-center gap-2">
                                                                  <RadioGroupItem id={id} value={String(value)} />
                                                                  <Label htmlFor={id}>{label}</Label>
                                                            </div>
                                                      ))}
                                                </RadioGroup>
                                          </div>
                                          {key === "pos" && currentValue === "petpooja" && (
                                                <>
                                                      <div className="flex gap-4 items-center text-md">
                                                            <Label className="w-[400px] text-md text-typography-400">Petpooja Menu ID:</Label>
                                                            <input
                                                                  type="text"
                                                                  value={pendingDelivery.menuId !== undefined ? (pendingDelivery.menuId ?? "") : (config.menuId ?? "")}
                                                                  onChange={(e) => {
                                                                        if (isEditingDelivery) {
                                                                              updatePending("delivery", "menuId", e.target.value);
                                                                              console.log(`Menu ID set to ${e.target.value}`);
                                                                        }
                                                                  }}
                                                                  placeholder="eg. otsmbu6q4n"
                                                                  disabled={!isEditingDelivery}
                                                                  className="border border-neutral-400 rounded-md p-1 px-2"
                                                            />
                                                            {validationErrors?.menuId && (
                                                                  <p className="text-red-500 text-sm">{validationErrors.menuId}</p>
                                                            )}
                                                      </div>
                                                      <div className="flex gap-4 items-center text-md">
                                                            <Label className="w-[400px] text-md text-typography-400">Petpooja Customer ID:</Label>
                                                            <input
                                                                  type="text"
                                                                  value={pendingDelivery.posCustId !== undefined ? (pendingDelivery.posCustId ?? "") : (config.posCustId ?? "")}
                                                                  onChange={(e) => {
                                                                        if (isEditingDelivery) {
                                                                              updatePending("delivery", "posCustId", e.target.value);
                                                                              console.log(`Pos Customer ID set to ${e.target.value}`);
                                                                        }
                                                                  }}
                                                                  placeholder="eg. 4818"
                                                                  disabled={!isEditingDelivery}
                                                                  className="border border-neutral-400 rounded-md p-1 px-2"
                                                            />
                                                            {validationErrors?.posCustId && (
                                                                  <p className="text-red-500 text-sm">{validationErrors.posCustId}</p>
                                                            )}
                                                      </div>
                                                      <div className="flex gap-4 items-center text-md">
                                                            <Label className="w-[400px] text-md text-typography-400">Petpooja Restaurant Name:</Label>
                                                            <input
                                                                  type="text"
                                                                  value={pendingDelivery.posRetName !== undefined ? (pendingDelivery.posRetName ?? "") : (config.posRetName ?? "")}
                                                                  onChange={(e) => {
                                                                        if (isEditingDelivery) {
                                                                              updatePending("delivery", "posRetName", e.target.value);
                                                                              console.log(`Pos Restaurant Name set to ${e.target.value}`);
                                                                        }
                                                                  }}
                                                                  placeholder=""
                                                                  disabled={!isEditingDelivery}
                                                                  className="border border-neutral-400 rounded-md p-1 px-2"
                                                            />
                                                            {validationErrors?.posRetName && (
                                                                  <p className="text-red-500 text-sm">{validationErrors.posRetName}</p>
                                                            )}
                                                      </div>
                                                      <div className="flex gap-4 items-center text-md">
                                                            <Label className="w-[400px] text-md text-typography-400">Petpooja Restaurant Contact No:</Label>
                                                            <input
                                                                  type="text"
                                                                  value={pendingDelivery.posRetContactNo !== undefined ? (pendingDelivery.posRetContactNo ?? "") : (config.posRetContactNo ?? "")}
                                                                  onChange={(e) => {
                                                                        if (isEditingDelivery) {
                                                                              updatePending("delivery", "posRetContactNo", e.target.value);
                                                                              console.log(`Pos Restaurant Contact No set to ${e.target.value}`);
                                                                        }
                                                                  }}
                                                                  placeholder=""
                                                                  disabled={!isEditingDelivery}
                                                                  className="border border-neutral-400 rounded-md p-1 px-2"
                                                            />
                                                            {validationErrors?.posRetContactNo && (
                                                                  <p className="text-red-500 text-sm">{validationErrors.posRetContactNo}</p>
                                                            )}
                                                      </div>
                                                      <div className="flex gap-4 items-center text-md">
                                                            <Label className="w-[400px] text-md text-typography-400">Petpooja Restaurant Address:</Label>
                                                            <input
                                                                  type="text"
                                                                  value={pendingDelivery.posRetAddress !== undefined ? (pendingDelivery.posRetAddress ?? "") : (config.posRetAddress ?? "")}
                                                                  onChange={(e) => {
                                                                        if (isEditingDelivery) {
                                                                              updatePending("delivery", "posRetAddress", e.target.value);
                                                                              console.log(`Pos Restaurant Address set to ${e.target.value}`);
                                                                        }
                                                                  }}
                                                                  placeholder=""
                                                                  disabled={!isEditingDelivery}
                                                                  className="border border-neutral-400 rounded-md p-1 px-2"
                                                            />
                                                            {validationErrors?.posRetAddress && (
                                                                  <p className="text-red-500 text-sm">{validationErrors.posRetAddress}</p>
                                                            )}
                                                      </div>
                                                </>
                                          )
                                          }
                                          {key == "ondcDomain" && currentValue == "RET11" && (
                                                <>
                                                      <div className="flex gap-4 items-center text-md">
                                                            <Label className="w-[400px] text-md text-typography-400">Default Order Prep Time (mins):</Label>
                                                            <input
                                                                  type="number"
                                                                  min={0}
                                                                  value={pendingDelivery.defaultOrderPrepTime !== undefined ? (pendingDelivery.defaultOrderPrepTime ?? "") : (config.defaultOrderPrepTime ?? "")}
                                                                  onChange={(e) => {
                                                                        if (isEditingDelivery) {
                                                                              updatePending("delivery", "defaultOrderPrepTime", e.target.value);
                                                                              console.log(`Default Order Prep Time set to ${e.target.value}`);
                                                                        }
                                                                  }}
                                                                  disabled={!isEditingDelivery}
                                                                  className="border border-neutral-400 rounded-md p-1 px-2"
                                                            />
                                                            {validationErrors?.defaultOrderPrepTime && (
                                                                  <p className="text-red-500 text-sm">{validationErrors.defaultOrderPrepTime}</p>
                                                            )}
                                                      </div>
                                                </>
                                          )
                                          }
                                    </>
                              );
                        })}
                        <div className="flex flex-col gap-2 mt-4">
                              <div className="text-lg text-typography-600">Timings:</div>
                              <div className="flex gap-4 items-center">
                                    <div className="flex gap-2 items-center w-[400px]">
                                          <Label className="w-[160px] text-md text-typography-400">Dispatch Time:</Label>
                                          <input
                                                type="time"
                                                value={pendingDelivery.dispatch_time !== undefined ? String(pendingDelivery.dispatch_time) : String(config.dispatch_time || "")}
                                                onChange={(e) => {
                                                      updatePending("delivery", "dispatch_time", e.target.value);
                                                      console.log(`Dispatch Time set to ${e.target.value}`);
                                                }}
                                                disabled={!isEditingDelivery}
                                                className="border border-neutral-400 rounded-md p-1 px-2"
                                          />
                                    </div>
                                    <div className="flex gap-2 items-center">
                                          <Label className="w-[160px] text-md text-typography-400">Delivery Time:</Label>
                                          <input
                                                type="time"
                                                value={pendingDelivery.delivery_time !== undefined ? String(pendingDelivery.delivery_time) : String(config.delivery_time || "")}
                                                onChange={(e) => {
                                                      updatePending("delivery", "delivery_time", e.target.value);
                                                      console.log(`Delivery Time set to ${e.target.value}`);
                                                }}
                                                disabled={!isEditingDelivery}
                                                className="border border-neutral-400 rounded-md p-1 px-2"
                                          />
                                    </div>
                              </div>
                              <div className="flex gap-4 items-center">
                                    <div className="flex gap-2 items-center w-[400px]">
                                          <Label className="w-[160px] text-md text-typography-400">Booking Open Time:</Label>
                                          <input
                                                type="number"
                                                min={0}
                                                value={
                                                      pendingDelivery.booking_open_time !== undefined
                                                            ? pendingDelivery.booking_open_time
                                                            : config.booking_open_time || 0
                                                }
                                                onKeyDown={(e) => {
                                                      if (e.key === "-" || e.key === "e") {
                                                            e.preventDefault();
                                                      }
                                                }}
                                                onChange={(e) => {
                                                      const value = parseFloat(e.target.value);
                                                      if (value >= 0) {
                                                            updatePending("delivery", "booking_open_time", value);
                                                            console.log(`Booking Open Time set to ${value} minutes`);
                                                      }
                                                }}
                                                disabled={!isEditingDelivery}
                                                className="border border-neutral-400 rounded-md p-1 px-2"
                                          />
                                    </div>
                                    <div className="flex gap-2 items-center">
                                          <Label className="w-[160px] text-md text-typography-400">Booking Close Time:</Label>
                                          <input
                                                type="number"
                                                min={0}
                                                value={
                                                      pendingDelivery.booking_close_time !== undefined
                                                            ? pendingDelivery.booking_close_time
                                                            : config.booking_close_time || 0
                                                }
                                                onKeyDown={(e) => {
                                                      if (e.key === "-" || e.key === "e") {
                                                            e.preventDefault();
                                                      }
                                                }}
                                                onChange={(e) => {
                                                      const value = parseFloat(e.target.value);
                                                      if (value >= 0) {
                                                            updatePending("delivery", "booking_close_time", value);
                                                            console.log(`Booking Close Time set to ${value} minutes`);
                                                      }
                                                }}
                                                disabled={!isEditingDelivery}
                                                className="border border-neutral-400 rounded-md p-1 px-2"
                                          />
                                    </div>
                              </div>
                              <div className="flex gap-4 items-center">
                                    <div className="flex gap-2 items-center w-[400px]">
                                          <Label className="w-[160px] text-md text-typography-400">Breakfast Open (t1Open):</Label>
                                          <input
                                                type="number"
                                                min={0}
                                                value={
                                                      pendingDelivery.t1Open !== undefined
                                                            ? pendingDelivery.t1Open
                                                            : config.t1Open || 0
                                                }
                                                onKeyDown={(e) => {
                                                      if (e.key === "-" || e.key === "e") {
                                                            e.preventDefault();
                                                      }
                                                }}
                                                onChange={(e) => {
                                                      const value = parseFloat(e.target.value);
                                                      if (value >= 0) {
                                                            updatePending("delivery", "t1Open", value);
                                                            console.log(`t1Open set to ${value} minutes`);
                                                      }
                                                }}
                                                disabled={!isEditingDelivery}
                                                className="border border-neutral-400 rounded-md p-1 px-2"
                                          />
                                    </div>
                                    <div className="flex gap-2 items-center">
                                          <Label className="w-[160px] text-md text-typography-400">Breakfast Close (t1Close):</Label>
                                          <input
                                                type="number"
                                                min={0}
                                                value={
                                                      pendingDelivery.t1Close !== undefined
                                                            ? pendingDelivery.t1Close
                                                            : config.t1Close || 0
                                                }
                                                onKeyDown={(e) => {
                                                      if (e.key === "-" || e.key === "e") {
                                                            e.preventDefault();
                                                      }
                                                }}
                                                onChange={(e) => {
                                                      const value = parseFloat(e.target.value);
                                                      if (value >= 0) {
                                                            updatePending("delivery", "t1Close", value);
                                                            console.log(`t1Close set to ${value} minutes`);
                                                      }
                                                }}
                                                disabled={!isEditingDelivery}
                                                className="border border-neutral-400 rounded-md p-1 px-2"
                                          />
                                    </div>
                              </div>
                              <div className="flex gap-4 items-center">
                                    <div className="flex gap-2 items-center w-[400px]">
                                          <Label className="w-[160px] text-md text-typography-400">Lunch Open (t2Open):</Label>
                                          <input
                                                type="number"
                                                min={0}
                                                value={
                                                      pendingDelivery.t2Open !== undefined
                                                            ? pendingDelivery.t2Open
                                                            : config.t2Open || 0
                                                }
                                                onKeyDown={(e) => {
                                                      if (e.key === "-" || e.key === "e") {
                                                            e.preventDefault();
                                                      }
                                                }}
                                                onChange={(e) => {
                                                      const value = parseFloat(e.target.value);
                                                      if (value >= 0) {
                                                            updatePending("delivery", "t2Open", value);
                                                            console.log(`t2Open set to ${value} minutes`);
                                                      }
                                                }}
                                                disabled={!isEditingDelivery}
                                                className="border border-neutral-400 rounded-md p-1 px-2"
                                          />
                                    </div>
                                    <div className="flex gap-2 items-center">
                                          <Label className="w-[160px] text-md text-typography-400">Lunch Close (t2Close):</Label>
                                          <input
                                                type="number"
                                                min={0}
                                                value={
                                                      pendingDelivery.t2Close !== undefined
                                                            ? pendingDelivery.t2Close
                                                            : config.t2Close || 0
                                                }
                                                onKeyDown={(e) => {
                                                      if (e.key === "-" || e.key === "e") {
                                                            e.preventDefault();
                                                      }
                                                }}
                                                onChange={(e) => {
                                                      const value = parseFloat(e.target.value);
                                                      if (value >= 0) {
                                                            updatePending("delivery", "t2Close", value);
                                                            console.log(`t2Close set to ${value} minutes`);
                                                      }
                                                }}
                                                disabled={!isEditingDelivery}
                                                className="border border-neutral-400 rounded-md p-1 px-2"
                                          />
                                    </div>
                              </div>
                              <div className="flex gap-4 items-center">
                                    <div className="flex gap-2 items-center w-[400px]">
                                          <Label className="w-[160px] text-md text-typography-400">Dinner Open (t3Open):</Label>
                                          <input
                                                type="number"
                                                min={0}
                                                value={
                                                      pendingDelivery.t3Open !== undefined
                                                            ? pendingDelivery.t3Open
                                                            : config.t3Open || 0
                                                }
                                                onKeyDown={(e) => {
                                                      if (e.key === "-" || e.key === "e") {
                                                            e.preventDefault();
                                                      }
                                                }}
                                                onChange={(e) => {
                                                      const value = parseFloat(e.target.value);
                                                      if (value >= 0) {
                                                            updatePending("delivery", "t3Open", value);
                                                            console.log(`t3Open set to ${value} minutes`);
                                                      }
                                                }}
                                                disabled={!isEditingDelivery}
                                                className="border border-neutral-400 rounded-md p-1 px-2"
                                          />
                                    </div>
                                    <div className="flex gap-2 items-center">
                                          <Label className="w-[160px] text-md text-typography-400">Dinner Close (t3Close):</Label>
                                          <input
                                                type="number"
                                                min={0}
                                                value={
                                                      pendingDelivery.t3Close !== undefined
                                                            ? pendingDelivery.t3Close
                                                            : config.t3Close || 0
                                                }
                                                onKeyDown={(e) => {
                                                      if (e.key === "-" || e.key === "e") {
                                                            e.preventDefault();
                                                      }
                                                }}
                                                onChange={(e) => {
                                                      const value = parseFloat(e.target.value);
                                                      if (value >= 0) {
                                                            updatePending("delivery", "t3Close", value);
                                                            console.log(`t3Close set to ${value} minutes`);
                                                      }
                                                }}
                                                disabled={!isEditingDelivery}
                                                className="border border-neutral-400 rounded-md p-1 px-2"
                                          />
                                    </div>
                              </div>
                        </div>
                        {isEditingDelivery && (
                              <div className="flex gap-4 justify-center mt-4">
                                    <button onClick={() => handleCancelSection("delivery")} className="px-4 py-2 border rounded-md">
                                          Cancel
                                    </button>
                                    <button onClick={() => handleSaveSection("delivery")} className="px-6 py-2 bg-primary text-white rounded-md">
                                          Save
                                    </button>
                              </div>
                        )}
                  </div>

                  {/* Business Configurations */}
                  <div className="flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow">
                        <div className="flex justify-between">
                              <div className="text-lg font-semibold text-typography-700">Business Configurations</div>
                              <button
                                    onClick={() => {
                                          if (!isEditingBusiness) {
                                                setPendingBusiness({
                                                      listing_seq: config.listing_seq,
                                                      mininum_required_balance: config.mininum_required_balance,
                                                      wa_enable: config.wa_enable,
                                                });
                                                console.log("Entering edit mode for Business");
                                          }
                                          setIsEditingBusiness(prev => !prev);
                                    }}
                                    className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800"
                              >
                                    {isEditingBusiness ? (
                                          <>
                                                <X className="h-4 w-4" /> Cancel
                                          </>
                                    ) : (
                                          <>
                                                <Edit className="h-4 w-4" /> Edit
                                          </>
                                    )}
                              </button>
                        </div>
                        <div className="flex gap-4 items-center">
                              <Label className="w-[400px] text-md text-typography-400">Business ID:</Label>
                              <input
                                    type="text"
                                    value={String(config.business_id)}
                                    disabled
                                    className="border border-neutral-400 rounded-md p-1 px-2 bg-gray-100"
                              />
                        </div>
                        {businessConfigFields.map(({ label, key, type, prefix }) => {
                              const k = key as keyof SellerConfig;
                              const currentValue = isEditingBusiness
                                    ? (pendingBusiness[k] !== undefined ? pendingBusiness[k] : config[k])
                                    : config[k];
                              return (
                                    <div key={key} className="flex gap-4 items-center text-md">
                                          <Label className="w-[400px] text-md text-typography-400">{label}</Label>
                                          <span className="font-semibold flex gap-2 items-center">
                                                {prefix}
                                                <input
                                                      type={type}
                                                      value={String(isEditingBusiness ? (pendingBusiness[k] ?? config[k]) : config[k] || "")}
                                                      onChange={(e) => {
                                                            if (isEditingBusiness) {
                                                                  updatePending("business", k, e.target.value);
                                                                  console.log(`Business onChange: ${key} set to ${e.target.value}`);
                                                            }
                                                      }}
                                                      disabled={!isEditingBusiness}
                                                      className="border border-neutral-400 rounded-md p-1 px-2"
                                                />
                                          </span>
                                    </div>
                              );
                        })}
                        {businessConfigRadioFields.map(({ label, key }) => {
                              const k = key as keyof SellerConfig;
                              const currentValue = isEditingBusiness
                                    ? (pendingBusiness[k] !== undefined ? pendingBusiness[k] : config[k])
                                    : config[k];
                              return (
                                    <div key={key} className="flex gap-4 items-center text-md">
                                          <Label className="w-[400px] text-md text-typography-400">{label}</Label>
                                          <RadioGroup
                                                value={currentValue ? "yes" : "no"}
                                                onValueChange={(val) => {
                                                      if (isEditingBusiness) {
                                                            updatePending("business", k, val === "yes");
                                                            console.log(`Business onChange: ${key} set to ${val === "yes"}`);
                                                      }
                                                }}
                                                disabled={!isEditingBusiness}
                                                className="flex gap-4 items-center text-md font-semibold text-typography-800"
                                          >
                                                <div className="flex items-center gap-2">
                                                      <RadioGroupItem id={`${key}-yes`} value="yes" />
                                                      <Label htmlFor={`${key}-yes`}>Yes</Label>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                      <RadioGroupItem id={`${key}-no`} value="no" />
                                                      <Label htmlFor={`${key}-no`}>No</Label>
                                                </div>
                                          </RadioGroup>
                                    </div>
                              );
                        })}
                        {isEditingBusiness && (
                              <div className="flex gap-4 justify-center">
                                    <button onClick={() => handleCancelSection("business")} className="px-4 py-2 border rounded-md">
                                          Cancel
                                    </button>
                                    <button onClick={() => handleSaveSection("business")} className="px-6 py-2 bg-primary text-white rounded-md">
                                          Save
                                    </button>
                              </div>
                        )}
                  </div>

                  {/* Packaging Configurations */}
                  <div className="flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow">
                        <div className="flex justify-between">
                              <div className="text-lg font-semibold text-typography-700">Packaging Configurations</div>
                              <button
                                    onClick={() => {
                                          if (!isEditingPackaging) {
                                                setPendingPackaging({
                                                      packagingCharge: config.packagingCharge,
                                                      packagingChargeType: config.packagingChargeType,
                                                      packagingApplicableOn: config.packagingApplicableOn,
                                                });
                                                console.log("Entering edit mode for Packaging");
                                          }
                                          setIsEditingPackaging(prev => !prev);
                                    }}
                                    className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800"
                              >
                                    {isEditingPackaging ? (
                                          <>
                                                <X className="h-4 w-4" /> Cancel
                                          </>
                                    ) : (
                                          <>
                                                <Edit className="h-4 w-4" /> Edit
                                          </>
                                    )}
                              </button>
                        </div>

                        <div className="flex gap-4 items-center text-md">
                              <Label className="w-[400px] text-md text-typography-400">Packaging Charge:</Label>
                              <input
                                    type="text"
                                    value={pendingPackaging.packagingCharge !== undefined ? (pendingPackaging.packagingCharge ?? "") : (config.packagingCharge ?? "")}
                                    onChange={(e) => {
                                          if (isEditingPackaging) {
                                                updatePending("packaging", "packagingCharge", e.target.value);
                                                console.log(`Packaging Charge set to ${e.target.value}`);
                                          }
                                    }}
                                    disabled={!isEditingPackaging}
                                    className="border border-neutral-400 rounded-md p-1 px-2"
                              />
                              {validationErrors?.packagingCharge && (
                                    <p className="text-red-500 text-sm">{validationErrors.packagingCharge}</p>
                              )}
                        </div>

                        <div className="flex gap-4 items-center text-md">
                              <Label className="w-[400px] text-md text-typography-400">Packaging Charge Type:</Label>
                              <select
                                    value={pendingPackaging.packagingChargeType !== undefined ? pendingPackaging.packagingChargeType : config.packagingChargeType}
                                    onChange={(e) => {
                                          if (isEditingPackaging) {
                                                updatePending("packaging", "packagingChargeType", e.target.value);
                                                console.log(`Packaging Charge Type set to ${e.target.value}`);
                                          }
                                    }}
                                    disabled={!isEditingPackaging}
                                    className="border border-neutral-400 rounded-md p-1 px-2"
                              >
                                    <option value="PERCENTAGE">Percentage</option>
                                    <option value="FIXED">Fixed</option>
                              </select>
                              {validationErrors?.packagingChargeType && (
                                    <p className="text-red-500 text-sm">{validationErrors.packagingChargeType}</p>
                              )}
                        </div>

                        <div className="flex gap-4 items-center text-md">
                              <Label className="w-[400px] text-md text-typography-400">Packaging Applicable On:</Label>
                              <select
                                    value={pendingPackaging.packagingApplicableOn !== undefined ? pendingPackaging.packagingApplicableOn : config.packagingApplicableOn}
                                    onChange={(e) => {
                                          if (isEditingPackaging) {
                                                updatePending("packaging", "packagingApplicableOn", e.target.value);
                                                console.log(`Packaging Applicable On set to ${e.target.value}`);
                                          }
                                    }}
                                    disabled={!isEditingPackaging}
                                    className="border border-neutral-400 rounded-md p-1 px-2"
                              >
                                    <option value="NONE">None</option>
                                    <option value="ITEM">Item</option>
                                    <option value="ORDER">Order</option>
                              </select>
                              {validationErrors?.packagingApplicableOn && (
                                    <p className="text-red-500 text-sm">{validationErrors.packagingApplicableOn}</p>
                              )}
                        </div>

                        {isEditingPackaging && (
                              <div className="flex gap-4 justify-center">
                                    <button onClick={() => handleCancelSection("packaging")} className="px-4 py-2 border rounded-md">
                                          Cancel
                                    </button>
                                    <button onClick={() => handleSaveSection("packaging")} className="px-6 py-2 bg-primary text-white rounded-md">
                                          Save
                                    </button>
                              </div>
                        )}
                  </div>


                  {/* Inventory Configurations */}
                  <div className="flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow">
                        <div className="flex justify-between">
                              <div className="text-lg font-semibold text-typography-700">Inventory Configurations</div>
                              <button
                                    onClick={() => {
                                          if (!isEditingInventory) {
                                                setPendingInventory({
                                                      auto_activate: config.auto_activate,
                                                      approx_pricing: config.approx_pricing,
                                                      approxPriceVisibility: config.approxPriceVisibility,
                                                      strikeoff_enabled: config.strikeoff_enabled,
                                                      contract_price_enabled: config.contract_price_enabled,
                                                      fav_items_enabled: config.fav_items_enabled,
                                                      minimum_order_qty: config.minimum_order_qty,
                                                      advance_booking_days: config.advance_booking_days,
                                                      minimum_order_value: config.minimum_order_value,
                                                      category_level: config.category_level,
                                                      miSources: config.miSources,
                                                      logisticProvider: config.logisticProvider,
                                                });
                                                console.log("Entering edit mode for Inventory");
                                          }
                                          setIsEditingInventory(prev => !prev);
                                    }}
                                    className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800"
                              >
                                    {isEditingInventory ? (
                                          <>
                                                <X className="h-4 w-4" /> Cancel
                                          </>
                                    ) : (
                                          <>
                                                <Edit className="h-4 w-4" /> Edit
                                          </>
                                    )}
                              </button>
                        </div>
                        {inventoryConfigRadioFields.map(({ label, key }) => {
                              const k = key as keyof SellerConfig;
                              const currentValue = isEditingInventory
                                    ? (pendingInventory[k] !== undefined ? pendingInventory[k] : config[k])
                                    : config[k];
                              return (
                                    <div key={key} className="flex gap-4 items-center text-md">
                                          <Label className="w-[400px] text-md text-typography-400">{label}</Label>
                                          <RadioGroup
                                                value={currentValue ? ["auto_activate"].includes(key) ? "no" : "yes" : ["auto_activate"].includes(key) ? "yes" : "no"}
                                                onValueChange={(val) => {
                                                      if (isEditingInventory) {
                                                            if (k === "auto_activate") {
                                                                  updatePending("inventory", "auto_activate", val === "no");
                                                                  console.log(`Inventory onChange: ${key} set to ${val === "no"}`);
                                                            } else {
                                                                  updatePending("inventory", k, val === "yes");
                                                                  console.log(`Inventory onChange: ${key} set to ${val === "yes"}`);
                                                            }
                                                      }
                                                }}
                                                disabled={!isEditingInventory}
                                                className="flex gap-4 items-center text-md font-semibold text-typography-800"
                                          >
                                                <div className="flex items-center gap-2">
                                                      <RadioGroupItem id={`${key}-yes`} value="yes" />
                                                      <Label htmlFor={`${key}-yes`}>Yes</Label>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                      <RadioGroupItem id={`${key}-no`} value="no" />
                                                      <Label htmlFor={`${key}-no`}>No</Label>
                                                </div>
                                          </RadioGroup>
                                    </div>
                              );
                        })}
                        <div className="flex gap-4 items-center text-md">
                              <Label className="w-[400px] text-md text-typography-400">Category Level:</Label>
                              <RadioGroup
                                    value={isEditingInventory
                                          ? (pendingInventory.category_level !== undefined ? String(pendingInventory.category_level) : String(config.category_level))
                                          : String(config.category_level)}
                                    onValueChange={(val) => {
                                          if (isEditingInventory) {
                                                updatePending("inventory", "category_level", Number(val));
                                                console.log(`Inventory onChange: category_level set to ${val}`);
                                          }
                                    }}
                                    disabled={!isEditingInventory}
                                    className="flex gap-4 items-center text-md font-semibold text-typography-800"
                              >
                                    <div className="flex items-center gap-2">
                                          <RadioGroupItem id="category_level-0" value="0" />
                                          <Label htmlFor="category_level-0">No Categories</Label>
                                    </div>
                                    <div className="flex items-center gap-2">
                                          <RadioGroupItem id="category_level-1" value="1" />
                                          <Label htmlFor="category_level-1">Level 1</Label>
                                    </div>
                                    <div className="flex items-center gap-2">
                                          <RadioGroupItem id="category_level-2" value="2" />
                                          <Label htmlFor="category_level-2">Level 2</Label>
                                    </div>
                                    <div className="flex items-center gap-2">
                                          <RadioGroupItem id="category_level-3" value="3" />
                                          <Label htmlFor="category_level-3">Level 3</Label>
                                    </div>
                              </RadioGroup>
                        </div>
                        {inventoryConfigFields.map(({ label, key, type, prefix, suffix, options }) => {
                              const k = key as keyof SellerConfig;
                              const currentValue = isEditingInventory
                                    ? (pendingInventory[k] !== undefined ? pendingInventory[k] : config[k])
                                    : config[k];

                              return (
                                    <div key={key} className="flex gap-4 items-center text-md">
                                          <Label className="w-[400px] text-md text-typography-400">{label}</Label>
                                          <span className="font-semibold flex gap-2 items-center">
                                                {prefix}
                                                {type === "select" ? (
                                                      <select
                                                            value={String(isEditingInventory ? (pendingInventory[k] ?? config[k]) : config[k] || "")}
                                                            onChange={(e) => {
                                                                  if (isEditingInventory) {
                                                                        updatePending("inventory", k, e.target.value);
                                                                        console.log(`Inventory onChange: ${key} set to ${e.target.value}`);
                                                                  }
                                                            }}
                                                            disabled={!isEditingInventory}
                                                            className="border border-neutral-400 rounded-md p-1 px-2"
                                                      >
                                                            <option value="" hidden>
                                                                  Select an option
                                                            </option>
                                                            {options?.map((option) => (
                                                                  <option key={option.value} value={option.value}>
                                                                        {option.label}
                                                                  </option>
                                                            ))}
                                                      </select>
                                                ) : (
                                                      <input
                                                            type={type}
                                                            value={String(isEditingInventory ? (pendingInventory[k] ?? config[k]) : config[k] || "")}
                                                            onChange={(e) => {
                                                                  if (isEditingInventory) {
                                                                        updatePending("inventory", k, e.target.value);
                                                                        console.log(`Inventory onChange: ${key} set to ${e.target.value}`);
                                                                  }
                                                            }}
                                                            disabled={!isEditingInventory}
                                                            className="border border-neutral-400 rounded-md p-1 px-2"
                                                      />
                                                )}
                                                {suffix}
                                          </span>
                                    </div>
                              );
                        })}
                        {isEditingInventory && (
                              <div className="flex gap-4 justify-center">
                                    <button onClick={() => handleCancelSection("inventory")} className="px-4 py-2 border rounded-md">
                                          Cancel
                                    </button>
                                    <button onClick={() => handleSaveSection("inventory")} className="px-6 py-2 bg-primary text-white rounded-md">
                                          Save
                                    </button>
                              </div>
                        )}
                  </div>
            </div>
      );
}