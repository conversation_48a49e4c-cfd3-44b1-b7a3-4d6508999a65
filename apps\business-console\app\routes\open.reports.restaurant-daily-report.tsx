import { json, LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData  } from "@remix-run/react";
import jwt from "jsonwebtoken";



export async function loader({ request }: LoaderFunctionArgs) {
      const METABASE_SECRET_KEY = process.env.METABASE_SECRET_KEY ;
      const METABASE_SITE_URL = process.env.METABASE_SITE_URL;

      if (!METABASE_SECRET_KEY) {
            throw new Error("Metabase secret key is not configured.");
      }

      const url = new URL(request.url);
      const sellerId = url.searchParams.get("seller_id");
      const sellerName = url.searchParams.get("seller_name");

      if (!sellerId) {
            throw new Error("Seller ID is required.");
      }

      const orderDate = url.searchParams.get("order_date") || new Date().toISOString().split('T')[0];

      const payload = {
            resource: { question: 70 },
            params: {
                  seller_id: sellerId,
                  order_date: orderDate,
            },
            exp: Math.round(Date.now() / 1000) + (10 * 60) // 10 minute expiration
      };

      const token = jwt.sign(payload, METABASE_SECRET_KEY);
      const embedUrl = METABASE_SITE_URL + "/embed/question/" + token +
            "#bordered=true&titled=true";

      return json({ embedUrl, sellerId, orderDate , sellerName});
}

export default function MetaRestaurantReport() {
      const { embedUrl, sellerId, orderDate , sellerName} = useLoaderData<typeof loader>();
   

      return (
            <div className="container mx-auto p-4">
                  <h1 className="text-2xl font-bold mb-2">Daily Report</h1>
                  {/* <p className="text-gray-500 mb-2">Seller ID: {sellerId}</p> */}
                  <p className="text-gray-500 mb-2">Order Date: {orderDate}</p>

                  <div className="w-full">
                        {embedUrl ? (
                              <iframe
                                    id="metabase-iframe"
                                    src={embedUrl}
                                    title="Metabase Dashboard"
                                    className="w-full h-screen border-0"
                              />
                        ) : (
                              <div className="p-6 text-center text-red-500">
                                    Failed to load the dashboard.
                              </div>
                        )}
                  </div>
            </div>
      );
}