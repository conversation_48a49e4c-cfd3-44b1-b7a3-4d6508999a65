import { Request, Response } from 'express';
import { CustomResponse } from '@interfaces/response.interface.js';

// import { logger } from '@/utils/logger.utils';
import FirebaseApiService from '@services/firebase.service.js';

class NotificationController {
  public firebaseApiService = new FirebaseApiService();


  public registerUser = async (req: Request, res: Response) => {
    try {
      const { mobileNumber, fcmToken, deviceId, lastUpdated } = req.body;

      if (!mobileNumber || !fcmToken) {
        res.status(400).json({ok: false, message: 'mobileNumber and fcmToken are required' });
        return;
      }
      const response = await this.firebaseApiService.registerUser(mobileNumber, fcmToken, deviceId, lastUpdated);
      if (response.ok) {
        return res.status(200).json(response);
      }

      return res.status(400).json({ok: false, err: response.err});
    } catch (error) {
        const err = error as Error;
        console.error(err.stack);
        return res.status(500).json({ok: false, err:"something went wrong"});
    }
  };

  public sendNotification = async (req: Request, res: Response) => {
    try {
      const { mobileNumbers, title, body, data }: { mobileNumbers: string[], title: string, body: string, data: unknown } = req.body;

      if (!mobileNumbers || !title) {
        res.status(400).json({ok: false, message: 'mobileNumber and fcmToken are required' });
        return;
      }
      const response = await this.firebaseApiService.sendNotification(mobileNumbers, title, body, data);
      if (response.ok) {
        return res.status(200).json(response);
      }

      return res.status(400).json({ok: false, err: response.err});
    } catch (error) {
      const err = error as Error;
      console.error(err.stack);
      return res.status(500).json({ok: false, err:"something went wrong"});
    }
  };
}

export default NotificationController;
