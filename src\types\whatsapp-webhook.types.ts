/**
 * WhatsApp Cloud API Webhook Types
 * Based on official Meta documentation: https://developers.facebook.com/docs/whatsapp/cloud-api/webhooks/payload-examples
 */

// Base webhook structure
export interface WhatsAppWebhookPayload {
    object: 'whatsapp_business_account';
    entry: WhatsAppWebhookEntry[];
}

export interface WhatsAppWebhookEntry {
    id: string; // WhatsApp Business Account ID
    changes: WhatsAppWebhookChange[];
}

export interface WhatsAppWebhookChange {
    value: WhatsAppWebhookValue;
    field: 'messages';
}

export interface WhatsAppWebhookValue {
    messaging_product: 'whatsapp';
    metadata: WhatsAppMetadata;
    contacts?: WhatsAppContact[];
    messages?: WhatsAppMessage[];
    statuses?: WhatsAppStatus[];
    errors?: WhatsAppError[];
}

// Metadata
export interface WhatsAppMetadata {
    display_phone_number: string;
    phone_number_id: string;
}

// Contact information
export interface WhatsAppContact {
    profile: {
        name: string;
    };
    wa_id: string;
}

// Message types
export type WhatsAppMessageType = 
    | 'text'
    | 'image'
    | 'audio'
    | 'video'
    | 'document'
    | 'sticker'
    | 'location'
    | 'contacts'
    | 'interactive'
    | 'button'
    | 'order'
    | 'system'
    | 'unknown';

// Base message interface
export interface WhatsAppMessage {
    from: string;
    id: string;
    timestamp: string;
    type: WhatsAppMessageType;
    context?: WhatsAppMessageContext;
    
    // Message type specific content
    text?: WhatsAppTextMessage;
    image?: WhatsAppMediaMessage;
    audio?: WhatsAppMediaMessage;
    video?: WhatsAppMediaMessage;
    document?: WhatsAppDocumentMessage;
    sticker?: WhatsAppMediaMessage;
    location?: WhatsAppLocationMessage;
    contacts?: WhatsAppContactMessage[];
    interactive?: WhatsAppInteractiveMessage;
    button?: WhatsAppButtonMessage;
    order?: WhatsAppOrderMessage;
    system?: WhatsAppSystemMessage;
}

// Message context (when replying to a message)
export interface WhatsAppMessageContext {
    from: string;
    id: string;
    mentions?: string[];
    quoted?: boolean;
    forwarded?: boolean;
    frequently_forwarded?: boolean;
}

// Text message
export interface WhatsAppTextMessage {
    body: string;
}

// Media messages (image, audio, video, sticker)
export interface WhatsAppMediaMessage {
    id?: string;
    mime_type?: string;
    sha256?: string;
    caption?: string;
}

// Document message
export interface WhatsAppDocumentMessage extends WhatsAppMediaMessage {
    filename?: string;
}

// Location message
export interface WhatsAppLocationMessage {
    latitude: number;
    longitude: number;
    name?: string;
    address?: string;
}

// Contact message
export interface WhatsAppContactMessage {
    addresses?: WhatsAppContactAddress[];
    birthday?: string;
    emails?: WhatsAppContactEmail[];
    name: WhatsAppContactName;
    org?: WhatsAppContactOrg;
    phones?: WhatsAppContactPhone[];
    urls?: WhatsAppContactUrl[];
}

export interface WhatsAppContactAddress {
    street?: string;
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
    country_code?: string;
    type?: 'HOME' | 'WORK';
}

export interface WhatsAppContactEmail {
    email?: string;
    type?: 'HOME' | 'WORK';
}

export interface WhatsAppContactName {
    formatted_name: string;
    first_name?: string;
    last_name?: string;
    middle_name?: string;
    suffix?: string;
    prefix?: string;
}

export interface WhatsAppContactOrg {
    company?: string;
    department?: string;
    title?: string;
}

export interface WhatsAppContactPhone {
    phone?: string;
    wa_id?: string;
    type?: 'HOME' | 'WORK';
}

export interface WhatsAppContactUrl {
    url?: string;
    type?: 'HOME' | 'WORK';
}

// Interactive message (buttons, lists, etc.)
export interface WhatsAppInteractiveMessage {
    type: 'button_reply' | 'list_reply';
    button_reply?: WhatsAppButtonReply;
    list_reply?: WhatsAppListReply;
}

export interface WhatsAppButtonReply {
    id: string;
    title: string;
}

export interface WhatsAppListReply {
    id: string;
    title: string;
    description?: string;
}

// Button message (legacy)
export interface WhatsAppButtonMessage {
    text: string;
    payload: string;
}

// Order message
export interface WhatsAppOrderMessage {
    catalog_id: string;
    product_items: WhatsAppProductItem[];
    text?: string;
}

export interface WhatsAppProductItem {
    product_retailer_id: string;
    quantity: number;
    item_price: number;
    currency: string;
}

// System message
export interface WhatsAppSystemMessage {
    body: string;
    type: 'customer_changed_number' | 'customer_identity_changed' | 'unknown';
    identity?: string;
    wa_id?: string;
    customer?: string;
}

// Status updates (message delivery status)
export interface WhatsAppStatus {
    id: string;
    status: 'sent' | 'delivered' | 'read' | 'failed';
    timestamp: string;
    recipient_id: string;
    conversation?: WhatsAppConversation;
    pricing?: WhatsAppPricing;
    errors?: WhatsAppError[];
}

export interface WhatsAppConversation {
    id: string;
    expiration_timestamp?: string;
    origin: {
        type: 'authentication' | 'marketing' | 'utility' | 'service' | 'referral_conversion';
    };
}

export interface WhatsAppPricing {
    billable: boolean;
    pricing_model: 'CBP' | 'NBP';
    category: 'authentication' | 'marketing' | 'utility' | 'service' | 'referral_conversion';
}

// Error structure
export interface WhatsAppError {
    code: number;
    title: string;
    message: string;
    error_data?: {
        details: string;
    };
}

// Type guards for message types
export function isTextMessage(message: WhatsAppMessage): message is WhatsAppMessage & { text: WhatsAppTextMessage } {
    return message.type === 'text' && !!message.text;
}

export function isImageMessage(message: WhatsAppMessage): message is WhatsAppMessage & { image: WhatsAppMediaMessage } {
    return message.type === 'image' && !!message.image;
}

export function isAudioMessage(message: WhatsAppMessage): message is WhatsAppMessage & { audio: WhatsAppMediaMessage } {
    return message.type === 'audio' && !!message.audio;
}

export function isVideoMessage(message: WhatsAppMessage): message is WhatsAppMessage & { video: WhatsAppMediaMessage } {
    return message.type === 'video' && !!message.video;
}

export function isDocumentMessage(message: WhatsAppMessage): message is WhatsAppMessage & { document: WhatsAppDocumentMessage } {
    return message.type === 'document' && !!message.document;
}

export function isStickerMessage(message: WhatsAppMessage): message is WhatsAppMessage & { sticker: WhatsAppMediaMessage } {
    return message.type === 'sticker' && !!message.sticker;
}

export function isLocationMessage(message: WhatsAppMessage): message is WhatsAppMessage & { location: WhatsAppLocationMessage } {
    return message.type === 'location' && !!message.location;
}

export function isContactMessage(message: WhatsAppMessage): message is WhatsAppMessage & { contacts: WhatsAppContactMessage[] } {
    return message.type === 'contacts' && !!message.contacts;
}

export function isInteractiveMessage(message: WhatsAppMessage): message is WhatsAppMessage & { interactive: WhatsAppInteractiveMessage } {
    return message.type === 'interactive' && !!message.interactive;
}

export function isButtonMessage(message: WhatsAppMessage): message is WhatsAppMessage & { button: WhatsAppButtonMessage } {
    return message.type === 'button' && !!message.button;
}

export function isOrderMessage(message: WhatsAppMessage): message is WhatsAppMessage & { order: WhatsAppOrderMessage } {
    return message.type === 'order' && !!message.order;
}

export function isSystemMessage(message: WhatsAppMessage): message is WhatsAppMessage & { system: WhatsAppSystemMessage } {
    return message.type === 'system' && !!message.system;
}

// Webhook validation types
export interface WebhookValidationHeaders {
    'x-hub-signature-256'?: string;
    'x-hub-signature'?: string;
}

export interface WebhookVerificationQuery {
    'hub.mode'?: string;
    'hub.verify_token'?: string;
    'hub.challenge'?: string;
} 