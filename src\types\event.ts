// File: types/event.ts
export interface BaseEventData {
  orderGroupId: number;
  orderAmount: number;
  orderDetail: Array<{
    itemName: string;
    qty: number;
    unit: string;
  }>;
}

export interface BuyerEventData extends BaseEventData {
  buyerName: string;
  primaryContactNumber: string;
  totalAmount: number;
}

export interface SellerEventData extends BaseEventData {
  buyerId: number;
  sellerId: number;
  sellerName: string;
  buyerName: string;
  address: string;
  deliveryDate: [number, number, number];
}

export interface TripOrderDetailEventData {
  orderId: number;
  itemId: number;
  itemName: string;
  packaging: string;
  itemUrl: string;
  itemRegionalLanguageName: string;
  unit: string;
  qty: number;
  price: number;
  amount: number;
  status: string;
  isReturnAllowed: boolean;
  cancelledQty: number;
  boxes: number;
  itemPicked: boolean;
  addOns?: Array<{
    id: number;
    name: string;
    items: Array<{
      id: number;
      name: string;
      price: number;
    }>;
  }>;
  posItemId: string;
  diet: string;
  variationSId?: string;
  variationName?: string;
  strikeOffAmount: number;
  itemTaxAmount?: number;
  variationId?: string;
}

export interface AddressEventData {
  id?: number;
  address: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  latitude?: string;
  longitude?: string;
  addressType?: string;
  isDefault?: boolean;
}

export interface TripOrderEventData {
  orderGroupId: number;
  orderAmount: number;
  inventoryId: number;
  buyerName: string;
  buyerId: number;
  sellerId: number;
  orderStatus: string;
  cashCollectionAmount: number;
  creditAmount: number;
  address: string;
  latitude: string;
  longitude: string;
  primaryContactNumber: string;
  secondContactNumber: string;
  buyerLocality: string;
  serviceAreaName: string;
  
  deliveryCharge: number;
  totalAmount: number;
  discountAmount: number;
  itemCount: number;
  weight: number;
  creditAllowed: boolean;
  cashAllowed: boolean;
  orderDetail: TripOrderDetailEventData[];
  
  delayPayment: boolean;
  delayPaymentPaid: boolean;
  delayPaymentPaidDirect: boolean;
  creditPendingAmount: number;
  
  deliveredTime: string;
  deliveredBy: string;
  deliveredByContactNumber: string;
  sellerName: string;
  estDeliveryTime: string;
  
  tripId: number;
  tripSeqNumber: number;
  
  salesExecutiveName: string;
  salesExecutiveMobile: string;
  deliveryDate: string;
  
  codTimeout: string;
  creditTimeout: string;
  
  shopOpenTime: number;
  totalBoxes: number;
  boxesGiven: number;
  boxesTaken: number;
  boxesBalance: number;
  approxPricing: boolean;
  pickedItemCount: number;
  pickedItemWeight: number;
  itemPickEnabled: boolean;
  totalOrderBoxes: number;
  totalOrderBags: number;
  deliveryType: string;
  delAssignedToName: string;
  delAssignedToUserId: number;
  defaultAddress?: AddressEventData;
  sellerMessage?: string;
  totalTaxAmount?: number;
  packagingCharges?: number;
  platformFee?: number;
  ondcDomain?: "RET10" | "RET11";
  networkType? : "B2B" | "B2C";
}

export interface SupportTicketEventData {
  ticketId: number;
  userId: number;
  userName: string;
  userMobileNo: string;
  ticketType: string;
  status: string;
  orderGroupId: number;
  description: string;
  closingRemarks: string;
  createdDate: string;
  distinctId: string;
  requestedCallBack: boolean;
  userType: string;
  lastModifiedDate: string;
  sellerId: number;
  sellerName: string;
}

export interface WabDayWiseReportEventData {
  sellerId: number;
  sellerName: string;
  deliveryDate: string;
  totalOrders: number;
  cancelledOrders: number;
  deliveredOrders: number;
  totalRevenue: number;
  totalEarnings: number;
  totalCustomers: number;
  newCustomers: number;
  repeatCustomers: number;
}

export type EventType = "OrderPlaced" | "OrderDispatched" | "OrderDelivered" | "OrderDeliveredWithCredit" | "OrderCancelled" | "PaymentReceived" | "NewSupportTicket" | "DailySalesReport" | "RiderNotFound";

export interface EventRequest {
  targetType: "mnet_seller" | "fm_buyer" | "wab_buyer" | "mnet_ops";
  targetWANumber: string;
  eventType: EventType;
  eventData: BuyerEventData | SellerEventData | TripOrderEventData | SupportTicketEventData | WabDayWiseReportEventData;
  wabPhoneNumberId?: string;
  wabToken?: string;
  wabNumber?: string;
  wabNetworkDomainId?: number;
}
