import {
    Home as HomeIcon,
    Users as UsersIcon,
    Truck,
    MapPin,
    MessageCircle,
    LayoutDashboard,
    Landmark,
    User as UserIcon,
    LogOut,
    Network,
    MessageCircleIcon,
    Contact,
    Handshake,
    Receipt,
    IdCard,
    Layers,
    Grape,
    ChartNetwork,
    ChartNoAxesCombined,
    User,
    HandCoins,
    Users,
    Tractor,
    UserRoundCog,
    LayoutList,
    Cherry,
    EarthLock,
    PlaneTakeoff,
    ChartPie,
    Store,
    Headset,
    Warehouse
} from "lucide-react";
import {
    Sidebar,
    SidebarContent,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuItem,
    SidebarMenuButton,
    SidebarMenuSub,
    SidebarMenuSubItem,
    SidebarMenuSubButton,
    SidebarProvider,
    SidebarRail,
    SidebarTrigger,
} from "@components/ui/sidebar";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@components/ui/alert-dialog";
import { Link, Navigate, Outlet, useLoaderData, useLocation, useSubmit } from "@remix-run/react";
import { destroySession, getSession } from "@utils/session.server";
import { json, LoaderFunction, redirect } from "@remix-run/node";
import { useEffect, useState } from "react";
import { withAuth, withResponse } from "@utils/auth-utils";

interface LoaderData {
    userPermissions: string[];
    userDetails: {
        userDetails: Record<string, any>;
        businessName: string;
    };
}

export const loader: LoaderFunction = withAuth(async ({ user }) => {
    return withResponse({
        userDetails: user || {},
        userPermissions: user?.userDetails?.roles || [],
    });
});

export const action = withAuth(async ({ request }) => {
    const session = await getSession(request.headers.get("Cookie"));
    return redirect("/login", {
        headers: {
            "Set-Cookie": await destroySession(session),
        },
    });
});

export default function Home() {
    const { userPermissions, userDetails } = useLoaderData<LoaderData>();
    const { pathname } = useLocation();
    const submit = useSubmit();
    const activeSection = pathname.split("/")[2];

    const [isNetWorkManagerBasic, setIsNetWorkManagerBasic] = useState(false);
    const [isSalesManagerBasic, setIsSalesManagerBasic] = useState(false);
    const [adminBasic, setAdminBasic] = useState(false);
    const [isSellerBasic, setIsSellerBasic] = useState(false);

    useEffect(() => {
        if (userPermissions) {
            setIsNetWorkManagerBasic(userPermissions.includes("OC_Manager"));
            setIsSalesManagerBasic(userPermissions.includes("FmSalesManager"));
            setIsSellerBasic(userPermissions.includes("SC_Basic"));
            setAdminBasic(userPermissions.includes("AC_Basic"));
        }
    }, [userPermissions]);

    const handleLogout = () => submit(null, { method: "post" });

    if (pathname === "/home") {
        return <Navigate to="/home/<USER>" replace />;
    }

    return (
        <SidebarProvider>
            <div className="flex h-screen w-screen bg-white">
                <Sidebar className="bg-white border-r ">
                    <SidebarHeader className="px-6 py-4 bg-white">
                        <div className="flex flex-col items-start gap-2">
                            <img src="/mnet-logo.svg" alt="mNet Logo" className="h-12 w-auto" />
                            <div className="flex items-center mt-8 w-full px-4 py-2 border rounded-md cursor-pointer hover:bg-gray-100 transition-colors">
                                <div className="flex items-center space-x-3">
                                    <div className="h-6 w-6 rounded-full bg-primary flex items-center justify-center text-sm font-bold text-white">
                                        {userDetails?.userDetails?.businessName?.[0]?.toUpperCase() || "B"}
                                    </div>
                                    <div className="text-gray-900 text-sm font-medium">
                                        {userDetails?.userDetails?.businessName || "Business Name"}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </SidebarHeader>

                    <SidebarContent className="bg-white">
                        <SidebarMenu className="mt-2 space-y-1">
                            {isSellerBasic && (
                                <SidebarMenuItem>
                                    <Link to="/home/<USER>">
                                        <SidebarMenuButton
                                            isActive={activeSection === "dashboard"}
                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "dashboard"
                                                ? "bg-blue-100 text-blue-800 font-semibold"
                                                : ""
                                                }`}
                                        >
                                            <HomeIcon className="h-5 w-5 mr-3" />
                                            Dashboard
                                        </SidebarMenuButton>
                                    </Link>
                                </SidebarMenuItem>
                            )}

                            {isSellerBasic && (
                                <SidebarMenuItem>
                                    <Link to="/home/<USER>">
                                        <SidebarMenuButton
                                            isActive={activeSection === "customers"}
                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "customers"
                                                ? "bg-blue-100 text-blue-800 font-semibold"
                                                : ""
                                                }`}
                                        >
                                            <Contact className="h-5 w-5 mr-3" />
                                            My Customers
                                        </SidebarMenuButton>
                                    </Link>
                                </SidebarMenuItem>
                            )}

                            {isSellerBasic && (
                                <SidebarMenuItem>
                                    <Link to="/home/<USER>">
                                        <SidebarMenuButton
                                            isActive={activeSection === "myTrips"}
                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "myTrips"
                                                ? "bg-blue-100 text-blue-800 font-semibold"
                                                : ""
                                                }`}
                                        >
                                            <Truck className="h-5 w-5 mr-3" />
                                            My Trips
                                        </SidebarMenuButton>
                                    </Link>
                                </SidebarMenuItem>
                            )}

                            {isSellerBasic && (
                                <SidebarMenuItem>
                                    <Link to="/home/<USER>">
                                        <SidebarMenuButton
                                            isActive={activeSection === "myItems"}
                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "myItems"
                                                ? "bg-blue-100 text-blue-800 font-semibold"
                                                : ""
                                                }`}
                                        >
                                            <Grape className="h-5 w-5 mr-3" />
                                            My Items
                                        </SidebarMenuButton>
                                    </Link>
                                </SidebarMenuItem>
                            )}

                            {isNetWorkManagerBasic && (
                                <SidebarMenuItem>
                                    <Link to="/home/<USER>">
                                        <SidebarMenuButton
                                            isActive={activeSection === "whatsappconnect"}
                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "whatsappconnect"
                                                ? "bg-blue-100 text-blue-800 font-semibold"
                                                : ""
                                                }`}
                                        >
                                            <MessageCircleIcon className="h-5 w-5 mr-3" />
                                            Whatsapp
                                        </SidebarMenuButton>
                                    </Link>
                                </SidebarMenuItem>
                            )}

          {isNetWorkManagerBasic && (
                                <SidebarMenuItem>
                                    <Link to="/home/<USER>">
                                        <SidebarMenuButton
                                            isActive={activeSection === "masterItemCategory"}
                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "masterItemCategory"
                                                ? "bg-blue-100 text-blue-800 font-semibold"
                                                : ""
                                                }`}
                                        >
                                            <Cherry className="h-5 w-5 mr-3" />
                                            LiveOrderDashBoard
                                        </SidebarMenuButton>
                                    </Link>
                                </SidebarMenuItem>
                            )}
                            {isNetWorkManagerBasic && (
                                <SidebarMenuItem>
                                    <Link to="/home/<USER>">
                                        <SidebarMenuButton
                                            isActive={activeSection === "masterItemCategory"}
                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "masterItemCategory"
                                                ? "bg-blue-100 text-blue-800 font-semibold"
                                                : ""
                                                }`}
                                        >
                                            <Cherry className="h-5 w-5 mr-3" />
                                            Master Item Category
                                        </SidebarMenuButton>
                                    </Link>
                                </SidebarMenuItem>
                            )}

                            {isNetWorkManagerBasic && (
                                <SidebarMenuItem>
                                    <Link to="/home/<USER>">
                                        <SidebarMenuButton
                                            isActive={activeSection === "masterItems"}
                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "masterItems"
                                                ? "bg-blue-100 text-blue-800 font-semibold"
                                                : ""
                                                }`}
                                        >
                                            <LayoutList className="h-5 w-5 mr-3" />
                                            Master Items
                                        </SidebarMenuButton>
                                    </Link>
                                </SidebarMenuItem>
                            )}

                            {isSellerBasic && (
                                <SidebarMenuItem>
                                    <Link to="/home/<USER>">
                                        <SidebarMenuButton
                                            isActive={activeSection === "sellerWiseSales"}
                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "sellerWiseSales"
                                                ? "bg-blue-100 text-blue-800 font-semibold"
                                                : ""
                                                }`}
                                        >
                                            <ChartNoAxesCombined className="h-5 w-5 mr-3" />
                                            Sales Report
                                        </SidebarMenuButton>
                                    </Link>
                                </SidebarMenuItem>
                            )}

                            {isSellerBasic && (
                                <SidebarMenuItem>
                                    <Link to="/home/<USER>">
                                        <SidebarMenuButton
                                            isActive={activeSection === "metaSalesReports"}
                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "metaSalesReports"
                                                ? "bg-blue-100 text-blue-800 font-semibold"
                                                : ""
                                                }`}
                                        >
                                            <ChartNetwork className="h-5 w-5 mr-3" />
                                            Sales Dashboard
                                        </SidebarMenuButton>
                                    </Link>
                                </SidebarMenuItem>
                            )}
                             {(isSellerBasic ) && (
                                <SidebarMenuItem>
                                    <SidebarMenuButton className="px-6 py-3 hover:bg-gray-100 text-gray-900 font-semibold rounded-md">
                                        <Warehouse className="h-5 w-5 mr-3" />
                                        StockManagement
                                    </SidebarMenuButton>
                                    <SidebarMenuSub>
                                        <SidebarMenuSubItem>
                                            <Link to="/home/<USER>">
                                                <SidebarMenuSubButton
                                                    isActive={activeSection === "mystock"}
                                                    className={`px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "salesAnalysis"
                                                        ? "bg-blue-100 text-blue-800 font-medium"
                                                        : ""
                                                        }`}
                                                >
                                                    My Stock
                                                </SidebarMenuSubButton>
                                            </Link>
                                        </SidebarMenuSubItem>
                                        <SidebarMenuSubItem>
                                            <Link to="/home/<USER>">
                                                <SidebarMenuSubButton
                                                    isActive={activeSection === "stockWithMe"}
                                                    className={`px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "sellerWiseSales"
                                                        ? "bg-blue-100 text-blue-800 font-medium"
                                                        : ""
                                                        }`}
                                                >
                                                    Stock With Me
                                                </SidebarMenuSubButton>
                                            </Link>
                                        </SidebarMenuSubItem>
                                    </SidebarMenuSub>
                                </SidebarMenuItem>
                            )}

                            {(isSalesManagerBasic || adminBasic) && (
                                <SidebarMenuItem>
                                    <SidebarMenuButton className="px-6 py-3 hover:bg-gray-100 text-gray-900 font-semibold rounded-md">
                                        <ChartPie className="h-5 w-5 mr-3" />
                                        Sales Related
                                    </SidebarMenuButton>
                                    <SidebarMenuSub>
                                        <SidebarMenuSubItem>
                                            <Link to="/home/<USER>">
                                                <SidebarMenuSubButton
                                                    isActive={activeSection === "salesAnalysis"}
                                                    className={`px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "salesAnalysis"
                                                        ? "bg-blue-100 text-blue-800 font-medium"
                                                        : ""
                                                        }`}
                                                >
                                                    Sales Analysis
                                                </SidebarMenuSubButton>
                                            </Link>
                                        </SidebarMenuSubItem>
                                        <SidebarMenuSubItem>
                                            <Link to="/home/<USER>">
                                                <SidebarMenuSubButton
                                                    isActive={activeSection === "sellerWiseSales"}
                                                    className={`px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "sellerWiseSales"
                                                        ? "bg-blue-100 text-blue-800 font-medium"
                                                        : ""
                                                        }`}
                                                >
                                                    Sales Report
                                                </SidebarMenuSubButton>
                                            </Link>
                                        </SidebarMenuSubItem>
                                        <SidebarMenuSubItem>
                                            <Link to="/home/<USER>">
                                                <SidebarMenuSubButton
                                                    isActive={activeSection === "fmMetaHourlySales"}
                                                    className={`px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "fmMetaHourlySales"
                                                        ? "bg-blue-100 text-blue-800 font-medium"
                                                        : ""
                                                        }`}
                                                >
                                                    FOS HourlyWise
                                                </SidebarMenuSubButton>
                                            </Link>
                                        </SidebarMenuSubItem>
                                        <SidebarMenuSubItem>
                                            <Link to="/home/<USER>">
                                                <SidebarMenuSubButton
                                                    isActive={activeSection === "sellerLevelreturns"}
                                                    className={`px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "sellerLevelreturns"
                                                        ? "bg-blue-100 text-blue-800 font-medium"
                                                        : ""
                                                        }`}
                                                >
                                                    Seller Level Returns
                                                </SidebarMenuSubButton>
                                            </Link>
                                        </SidebarMenuSubItem>
                                        <SidebarMenuSubItem>
                                            <Link to="/home/<USER>">
                                                <SidebarMenuSubButton
                                                    isActive={activeSection === "fosLevelReturns"}
                                                    className={`px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "fosLevelReturns"
                                                        ? "bg-blue-100 text-blue-800 font-medium"
                                                        : ""
                                                        }`}
                                                >
                                                    FOS Level Returns
                                                </SidebarMenuSubButton>
                                            </Link>
                                        </SidebarMenuSubItem>
                                    </SidebarMenuSub>
                                </SidebarMenuItem>
                            )}

                            {(isNetWorkManagerBasic || adminBasic) && (
                                <SidebarMenuItem>
                                    <Link to="/home/<USER>">
                                        <SidebarMenuButton
                                            isActive={activeSection === "bankTransactions"}
                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "bankTransactions"
                                                ? "bg-blue-100 text-blue-800 font-semibold"
                                                : ""
                                                }`}
                                        >
                                            <Landmark className="h-5 w-5 mr-3" />
                                            Bank Transactions
                                        </SidebarMenuButton>
                                    </Link>
                                </SidebarMenuItem>
                            )}

                            {isNetWorkManagerBasic && (
                                <SidebarMenuItem>
                                    <Link to="/home/<USER>">
                                        <SidebarMenuButton
                                            isActive={activeSection === "localities"}
                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "localities"
                                                ? "bg-blue-100 text-blue-800 font-semibold"
                                                : ""
                                                }`}
                                        >
                                            <MapPin className="h-5 w-5 mr-3" />
                                            Master Localities
                                        </SidebarMenuButton>
                                    </Link>
                                </SidebarMenuItem>
                            )}
                            {(isNetWorkManagerBasic || adminBasic) && <SidebarMenuItem>
                                <Link to="/home/<USER>">
                                    <SidebarMenuButton isActive={activeSection === "tickets"}
                                        className="px-6 py-6 hover:bg-gray-100 text-gray-900"
                                    >
                                        <Headset className="h-6 w-6 mr-3" />
                                        Support Tickets
                                    </SidebarMenuButton>
                                </Link>
                            </SidebarMenuItem>}

                            {(isNetWorkManagerBasic || isSalesManagerBasic || adminBasic) && (
                                <SidebarMenuItem>
                                    <Link to="/home/<USER>">
                                        <SidebarMenuButton
                                            isActive={activeSection === "networkManagement"}
                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "networkManagement"
                                                ? "bg-blue-100 text-blue-800 font-semibold"
                                                : ""
                                                }`}
                                        >
                                            <EarthLock className="h-5 w-5 mr-3" />
                                            Network Management
                                        </SidebarMenuButton>
                                    </Link>
                                </SidebarMenuItem>
                            )}

                            {(isNetWorkManagerBasic || adminBasic) && (
                                <SidebarMenuItem>
                                    <Link to="/home/<USER>">
                                        <SidebarMenuButton
                                            isActive={activeSection === "sellerManagement"}
                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "sellerManagement"
                                                ? "bg-blue-100 text-blue-800 font-semibold"
                                                : ""
                                                }`}
                                        >
                                            <PlaneTakeoff className="h-5 w-5 mr-3" />
                                            Seller Management
                                        </SidebarMenuButton>
                                    </Link>
                                </SidebarMenuItem>
                            )}
                            
                             {/* {(isNetWorkManagerBasic || adminBasic) && (
                                <SidebarMenuItem>
                                    <Link to="/home/<USER>">
                                        <SidebarMenuButton
                                            isActive={activeSection === "freeItemManagement"}
                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "sellerManagement"
                                                ? "bg-blue-100 text-blue-800 font-semibold"
                                                : ""
                                                }`}
                                        >
                                            <PlaneTakeoff className="h-5 w-5 mr-3" />
                                            Free Item Management
                                        </SidebarMenuButton>
                                    </Link>
                                </SidebarMenuItem>
                            )} */}

                            {(isNetWorkManagerBasic || adminBasic) && (
                                <SidebarMenuItem>
                                    <Link to="/home/<USER>">
                                        <SidebarMenuButton
                                            isActive={activeSection === "buyerManagement"}
                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "buyerManagement"
                                                ? "bg-blue-100 text-blue-800 font-semibold"
                                                : ""
                                                }`}
                                        >
                                            <Store className="h-5 w-5 mr-3" />
                                            Buyer Management
                                        </SidebarMenuButton>
                                    </Link>
                                </SidebarMenuItem>
                            )}

                            {(isNetWorkManagerBasic || adminBasic) && (
                                <SidebarMenuItem>
                                    <Link to="/home/<USER>">
                                        <SidebarMenuButton
                                            isActive={activeSection === "tripManagement"}
                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "tripManagement"
                                                ? "bg-blue-100 text-blue-800 font-semibold"
                                                : ""
                                                }`}
                                        >
                                            <Truck className="h-5 w-5 mr-3" />
                                            Trip Management
                                        </SidebarMenuButton>
                                    </Link>
                                </SidebarMenuItem>
                            )}
                            

                            <SidebarMenuItem className="mt-auto">
                                <AlertDialog>
                                    <AlertDialogTrigger asChild>
                                        <SidebarMenuButton className="px-6 py-3 hover:bg-gray-100 text-gray-900 transition-colors rounded-md">
                                            <LogOut className="h-5 w-5 mr-3" />
                                            Logout
                                        </SidebarMenuButton>
                                    </AlertDialogTrigger>
                                    <AlertDialogContent>
                                        <AlertDialogHeader>
                                            <AlertDialogTitle>Are you sure you want to logout?</AlertDialogTitle>
                                            <AlertDialogDescription>
                                                You will be redirected to the login page.
                                            </AlertDialogDescription>
                                        </AlertDialogHeader>
                                        <AlertDialogFooter>
                                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                                            <AlertDialogAction onClick={handleLogout}>Logout</AlertDialogAction>
                                        </AlertDialogFooter>
                                    </AlertDialogContent>
                                </AlertDialog>
                            </SidebarMenuItem>
                        </SidebarMenu>
                    </SidebarContent>
                    <SidebarRail />
                </Sidebar>

                <main className="flex-1 overflow-y-auto p-4">
                    <SidebarTrigger className="mb-4 lg:hidden text-gray-900 hover:bg-gray-100 p-2 rounded-md" />
                    <Outlet />
                </main>
            </div>
        </SidebarProvider>
    );
}