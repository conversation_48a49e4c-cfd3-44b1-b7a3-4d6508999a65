{"name": "business-console", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "dev": "remix vite:dev", "start": "remix-serve ./build/server/index.js", "typecheck": "tsc"}, "dependencies": {"@aws-sdk/client-s3": "^3.731.1", "@aws-sdk/lib-storage": "^3.731.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@googlemaps/polyline-codec": "^1.0.28", "@googlemaps/react-wrapper": "^1.1.42", "@headlessui/react": "^2.2.0", "@mui/lab": "^6.0.0-beta.26", "@mui/material": "^6.4.3", "@mui/x-date-pickers": "^7.26.0", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-tooltip": "^1.1.7", "@react-google-maps/api": "^2.20.3", "@remix-run/node": "^2.13.1", "@remix-run/react": "^2.13.1", "@remix-run/serve": "^2.13.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "firebase-admin": "^13.0.1", "html-to-image": "^1.11.13", "isbot": "^4.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.453.0", "pdf-lib": "^1.17.1", "qr-code-styling": "^1.9.2", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-icons": "^5.4.0", "recharts": "^2.13.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@remix-run/dev": "^2.13.1", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "autoprefixer": "^10.4.20", "eslint": "^8.38.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.14", "typescript": "^5.1.6", "vite": "^5.1.0", "vite-tsconfig-paths": "^4.3.2"}, "engines": {"node": ">=20.0.0"}, "overrides": {"@radix-ui/react-dismissable-layer": "^1.0.5", "@radix-ui/react-focus-scope": "^1.0.4"}}