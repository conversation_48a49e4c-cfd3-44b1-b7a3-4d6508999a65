# Time Utility Documentation

## Overview

The `TimeUtil` class provides simplified time handling utilities for webhook and notification services. It focuses on essential timestamp operations with IST timezone support.

## Features

- **Current timestamp generation** - Get current timestamp in milliseconds
- **IST timezone ISO formatting** - Convert timestamps to IST ISO strings  
- **Time range creation** - Create time ranges for filtering operations
- **Logging format** - Format timestamps for logging with IST timezone

## Basic Usage

### Current Timestamp

```typescript
import { TimeUtil } from '@/utils/time.util.js';

// Get current timestamp in milliseconds
const timestamp = TimeUtil.getCurrentTimestamp();
console.log(timestamp); // 1704023445123
```

### Converting to IST ISO

```typescript
// Convert timestamp (milliseconds) to IST ISO string
const timestamp = Date.now();
const istISO = TimeUtil.toISTISO(timestamp);
console.log(istISO); // "2024-01-01T10:30:45.123+05:30"
```

### Time Ranges for Filtering

```typescript
// Create time range for last 24 hours
const timeRange = TimeUtil.createTimeRange(24);
console.log(timeRange);
// {
//   startTimestamp: 1703937045123,  // 24 hours ago
//   endTimestamp: 1704023445123     // now
// }

// Use for database queries
const filter = {
    businessNumber: "1234567890",
    startTimestamp: timeRange.startTimestamp,
    endTimestamp: timeRange.endTimestamp
};
```

### Logging

```typescript
// Format current time for logging
console.log(`Processing completed at ${TimeUtil.formatForLogging()}`);
// "Processing completed at 2024-01-01 10:30:45 IST"
```

## Usage in Webhook/Notification Services

### Creating Logs

```typescript
// Webhook log creation
const timestamp = TimeUtil.getCurrentTimestamp();
const webhookLog: WebhookLog = {
    webhookId: `msg_${timestamp}_${randomId}`,
    timestamp,
    timestampISO: TimeUtil.toISTISO(timestamp),
    // ... other fields
    receivedAt: timestamp,
    receivedAtISO: TimeUtil.toISTISO(timestamp),
    lastUpdated: timestamp,
    lastUpdatedISO: TimeUtil.toISTISO(timestamp)
};
```

### Updating Status

```typescript
// Update processing status
const processedAt = TimeUtil.getCurrentTimestamp();
await repository.updateLog(webhookId, timestamp, {
    status: WebhookStatus.PROCESSED,
    processedAt,
    processedAtISO: TimeUtil.toISTISO(processedAt),
    processingDuration: processedAt - timestamp
});
```

### Analytics Queries

```typescript
// Get data for last 24 hours
const timeRange = TimeUtil.createTimeRange(24);
const analytics = await getAnalytics(
    businessNumber, 
    timeRange.startTimestamp,
    timeRange.endTimestamp
);
```

## Constants

```typescript
export const IST_TIMEZONE = 'Asia/Kolkata';
export const ISO_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSxxx";
```

## API Reference

### `TimeUtil.getCurrentTimestamp(): number`
Returns the current timestamp in milliseconds.

### `TimeUtil.toISTISO(timestampMs: number): string`
Converts a timestamp in milliseconds to an ISO string in IST timezone.

### `TimeUtil.createTimeRange(hoursBack: number): TimeRange`
Creates a time range from `hoursBack` hours ago to now.

### `TimeUtil.formatForLogging(): string`
Returns current time formatted for logging with IST timezone indicator.

## Migration from v1

The utility has been simplified. Here's what changed:

### Removed Functions
- `getCurrentTimestamps()` - Use `getCurrentTimestamp()` and `toISTISO()` separately
- `createTimestampPair()` - Not needed with simplified approach
- `calculateDuration()` - Use simple subtraction: `endTime - startTime`
- `getRelativeTime()` - Removed to focus on essential functions
- All Unix seconds conversions - Use milliseconds consistently

### Simplified Approach
```typescript
// Old (v1)
const timestamps = TimeUtil.getCurrentTimestamps();
// { unix: 1704023445, unixMs: 1704023445123, iso: "...", readable: "..." }

// New (v2)
const timestamp = TimeUtil.getCurrentTimestamp(); // 1704023445123
const iso = TimeUtil.toISTISO(timestamp);        // "2024-01-01T10:30:45.123+05:30"
```

## Best Practices

1. **Use millisecond timestamps** for all database storage
2. **Use `toISTISO()`** for human-readable ISO timestamps
3. **Use `createTimeRange()`** for filtering operations
4. **Use `formatForLogging()`** for console output
5. **Store both timestamp and timestampISO** in database entities

## Dependencies

- `date-fns-tz` - For IST timezone formatting
- `date-fns` - For time range calculations 