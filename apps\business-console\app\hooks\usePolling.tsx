import { useCallback, useRef, useState } from "react"

export function usePolling(callback: () => void, interval = 10000) {
  const [isPolling, setIsPolling] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  const startPolling = useCallback(() => {
    if (intervalRef.current) return // Already polling

    setIsPolling(true)
    intervalRef.current = setInterval(callback, interval)
  }, [callback, interval])

  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    setIsPolling(false)
  }, [])

  return { isPolling, startPolling, stopPolling }
}