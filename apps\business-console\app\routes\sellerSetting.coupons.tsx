import { json, LoaderFunction } from "@remix-run/node";
import {
  NavigateFunction,
  Outlet,
  useFetcher,
  useLoaderData,
  useNavigate,
} from "@remix-run/react";
import { format, parseISO } from "date-fns";
import { Edit2Icon, Trash2Icon, SearchIcon, XCircleIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Separator } from "~/components/ui/separator";
import { useToast } from "~/components/ui/ToastProvider";
import { deleteCoupon, getCoupons } from "~/services/coupons";
import { CouponDetailsType } from "~/types/api/businessConsoleService/coupons";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { camelCaseToWords } from "~/utils/format";

interface Loaderdata {
  data: CouponDetailsType[];
}

export const loader: LoaderFunction = withAuth(async ({ request }) => {
  try {
    const response = await getCoupons(request);
    return withResponse({ data: response.data }, response.headers);
  } catch (error) {
    console.error("Error in loader:", error);
    throw new Response("Failed to fetch coupons data", {
      status: 500,
    });
  }
});

export const action = withAuth(async ({ request }) => {
  const formData = await request.formData();
  const actionType = formData.get("actionType");
  const couponId = Number(formData.get("couponId"));

  if (actionType === "deleteCoupon") {
    try {
      const response = await deleteCoupon(couponId, request);
      return withResponse(
        { data: response.data, actionType: actionType, success: true },
        response.headers
      );
    } catch (error) {
      console.error("Error in action:", error);
      return json(
        { data: null, actionType: actionType, success: false },
        { status: 500 }
      );
    }
  }

  console.log("Invalid action type:", actionType);
  return json(
    { data: null, actionType: actionType, success: false },
    { status: 400 }
  );
});

export function CouponCard({
  coupon,
  fetcher,
  navigate,
}: {
  coupon: CouponDetailsType;
  fetcher: ReturnType<typeof useFetcher>;
  navigate: NavigateFunction;
}) {
  return (
    <div className="p-4 rounded-xl shadow-[0px_2px_8px_0px_rgba(0,0,0,0.1)] hover:shadow-[0px_4px_12px_0px_rgba(0,0,0,0.15)] transition-shadow duration-200 bg-white border border-neutral-100">
      <div className="mb-3">
        <div className="flex flex-row justify-between items-center">
          <div className="p-1 border border-neutral-600 rounded-md bg-[#FCFCFD]">
            <h3 className="text-base font-semibold text-typography-400">
              {coupon.couponCode}
            </h3>
          </div>

          <span
            className={`px-2 py-1 rounded-md text-xs font-medium ${
              coupon.active
                ? "bg-green-100 text-green-800"
                : "bg-red-100 text-red-800"
            }`}
          >
            {coupon.active === true
              ? "Active"
              : coupon.active === false
              ? "Expired"
              : ""}
          </span>
        </div>
        <p className="mt-2 text-sm text-typography-500 line-clamp-2">
          {coupon.description}
        </p>
      </div>

      <div className="border-b border-neutral-200" />

      <div className="my-3">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-typography-400 mb-1">Coupon Type</p>
            <p className="text-sm font-medium text-typography-700">
              {coupon.couponName
                ? camelCaseToWords(coupon.couponName)
                : "Not specified"}
            </p>
          </div>
          <div className="text-right">
            <p className="text-xs text-typography-400 mb-1">Valid Till</p>
            <p className="text-sm font-medium text-typography-700">
              {coupon.validTo
                ? format(parseISO(coupon.validTo), "dd MMMM yyyy")
                : "Not specified"}
            </p>
          </div>
        </div>
        <div className="mt-4 grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-typography-400 mb-1">Coupon Usage</p>
            <div className="flex flex-row items-center text-sm font-medium text-typography-700">
              <span className="mr-1">{coupon.totalUsers || "0"} Users</span>
              <Separator
                orientation="vertical"
                className="h-4 mx-1 bg-neutral-300"
              />
              <span className="ml-1">{coupon.totalOrders} Orders</span>
            </div>
          </div>
          <div className="text-right">
            <p className="text-xs text-typography-400 mb-1">Discount Amount</p>
            <p className="text-sm font-medium text-typography-700">
              ₹ {coupon.discountValue}
            </p>
          </div>
        </div>
      </div>

      <div className="border-b border-neutral-200" />

      <div className="relative mt-3 flex flex-row gap-3 justify-end">
        <button
          className="border border-neutral-200 rounded-full p-2 cursor-pointer hover:bg-primary-50 hover:border-primary-200 transition-colors duration-200"
          onClick={() => navigate(`/sellerSetting/coupon/${coupon.id}`)}
          aria-label="Edit coupon"
        >
          <Edit2Icon className="w-4 h-4 text-primary-600" />
        </button>
        <button
          className="border border-neutral-200 rounded-full p-2 cursor-pointer hover:bg-red-50 hover:border-red-200 transition-colors duration-200"
          onClick={() => {
            if (confirm("Are you sure you want to delete this coupon?")) {
              fetcher.submit(
                { actionType: "deleteCoupon", couponId: String(coupon.id) },
                { method: "DELETE" }
              );
            }
          }}
          aria-label="Delete coupon"
        >
          <Trash2Icon className="w-4 h-4 text-red-500" />
        </button>
      </div>
    </div>
  );
}

export default function Coupons() {
  const { data: coupons } = useLoaderData<Loaderdata>();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredCoupons, setFilteredCoupons] = useState<CouponDetailsType[]>(
    coupons || []
  );

  const fetcher = useFetcher<{
    data: void;
    success: boolean;
    actionType: string;
  }>();
  const { showToast } = useToast();

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
  };

  const clearSearch = () => {
    setSearchTerm("");
  };

  useEffect(() => {
    if (!coupons) return;

    if (searchTerm.trim() === "") {
      setFilteredCoupons(coupons);
    } else {
      const term = searchTerm.toLowerCase().trim();
      const filtered = coupons.filter((coupon) =>
        coupon.couponCode.toLowerCase().includes(term)
      );
      setFilteredCoupons(filtered);
    }
  }, [searchTerm, coupons]);

  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (
        fetcher.data.success === true &&
        fetcher.data.actionType === "deleteCoupon"
      ) {
        showToast("Coupon deleted successfully", "success");
        window.location.reload();
      } else if (
        fetcher.data.success === false &&
        fetcher.data.actionType === "deleteCoupon"
      ) {
        showToast("Failed to delete coupon", "error");
      }
    }
  }, [fetcher.state, fetcher.data]);

  return (
    <div className="max-w-6xl mx-auto">
      <div className="bg-[#F2F4F9] rounded-lg">
        <div
          aria-labelledby="coupons-heading"
          className="flex flex-row gap-3 items-center p-4"
        >
          <button
            className="w-7 h-7 cursor-pointer hover:bg-neutral-100 rounded-full p-1 transition-colors duration-200"
            onClick={() => navigate("/sellerSetting/customerAnalysis")}
            aria-label="Go back"
          >
            <svg
              width="100%"
              height="100%"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M7.13805 3.36193C7.3984 3.62228 7.3984 4.04439 7.13805 4.30474L3.60946 7.83333H14C14.3682 7.83333 14.6666 8.13181 14.6666 8.5C14.6666 8.86819 14.3682 9.16667 14 9.16667H3.60946L7.13805 12.6953C7.3984 12.9556 7.3984 13.3777 7.13805 13.6381C6.8777 13.8984 6.45559 13.8984 6.19524 13.6381L1.52858 8.97141C1.26823 8.71106 1.26823 8.28895 1.52858 8.0286L6.19524 3.36193C6.45559 3.10158 6.8777 3.10158 7.13805 3.36193Z"
                fill="#1F2A37"
              />
            </svg>
          </button>
          <h2 className="text-xl font-semibold text-typography-700">Coupons</h2>
        </div>
        <div aria-labelledby="coupons-search" className="px-4 pb-4">
          <div className="relative">
            <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
              <SearchIcon className="h-4 w-4 text-gray-400" />
            </div>
            <Input
              type="text"
              placeholder="Have a coupon code? Type here"
              className="border border-neutral-200 rounded-full font-medium text-typography-500 w-full pl-10 pr-10"
              value={searchTerm}
              onChange={handleSearchChange}
            />
            {searchTerm && (
              <div className="absolute inset-y-0 right-3 flex items-center">
                <button
                  onClick={clearSearch}
                  className="text-gray-400 hover:text-gray-600"
                  aria-label="Clear search"
                >
                  <XCircleIcon className="h-4 w-4" />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      <div
        aria-labelledby="coupons-list"
        className="px-2 sm:px-4 pt-6 pb-20 md:pb-5"
      >
        {filteredCoupons.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {filteredCoupons.map((coupon) => (
              <CouponCard
                key={coupon.id}
                coupon={coupon}
                fetcher={fetcher}
                navigate={navigate}
              />
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-10 text-center">
            {searchTerm ? (
              <>
                <p className="text-typography-500 mb-2">
                  No coupons found matching "{searchTerm}"
                </p>
                <p className="text-typography-400 text-sm">
                  Try a different search term or clear the search
                </p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={clearSearch}
                >
                  Clear Search
                </Button>
              </>
            ) : (
              <>
                <p className="text-typography-500 mb-2">No coupons found</p>
                <p className="text-typography-400 text-sm">
                  Create your first coupon to get started
                </p>
              </>
            )}
          </div>
        )}
      </div>

      <div className="fixed bottom-0 left-0 right-0 z-30 p-4 flex justify-center md:static md:justify-end md:border-0">
        <Button
          onClick={() => navigate("/sellerSetting/coupon/add")}
          className="w-full md:w-auto font-semibold flex items-center justify-center rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 bg-primary p-3 hover:bg-primary"
        >
          Add New Coupon
        </Button>
      </div>
    </div>
  );
}
