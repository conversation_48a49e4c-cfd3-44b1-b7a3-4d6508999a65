import { LoaderFunction, redirect } from "@remix-run/node";
import { Link, Navigate, Outlet, useLoaderData, useLocation } from "@remix-run/react";
import { ChartNoAxesCombined, GalleryHorizontal, Menu } from "lucide-react"

import { useState, useEffect } from "react";
import { FaWhatsapp } from "react-icons/fa";
// eslint-disable-next-line import/no-unresolved
import { Button } from "~/components/ui/button";
// eslint-disable-next-line import/no-unresolved
import { Sheet, SheetContent, SheetTrigger } from "~/components/ui/sheet";
// eslint-disable-next-line import/no-unresolved
import { withAuth, withResponse } from "~/utils/auth-utils";
// eslint-disable-next-line import/no-unresolved
import { destroySession, getSession } from "~/utils/session.server";

interface LoaderData {
      userPermissions: string[];
      userDetails: {
            userDetails: {
                  businessName?: string;
            };
      };
}
export const loader: LoaderFunction = withAuth(
      async ({ user }) => {
            return withResponse({
                  userPermissions: user?.userPermissions || [],
                  userDetails: user || { userDetails: {} },
            });
      }
)
export const action = withAuth(
      async ({ request }) => {
            const session = await getSession(request.headers.get("Cookie"))
            return redirect("/login", {
                  headers: {
                        "Set-Cookie": await destroySession(session),
                  },
            })
      }
);
export default function SellerSetting() {
      const loaderData = useLoaderData<LoaderData>();
      const location = useLocation();
      const [isOpen, setIsOpen] = useState(false);
      const [shouldRedirect, setShouldRedirect] = useState(false);
      const activeSection = location.pathname.split("/")[2];
      console.log("active", activeSection)

      useEffect(() => {
            if (location.pathname === "/sellerSetting") {
                  setShouldRedirect(true);
            } else {
                  setShouldRedirect(false);
            }
      }, [location.pathname]);

      if (shouldRedirect) {
            return <Navigate to="/sellerSetting/customerAnalysis" replace />;
      }
      const NavContent = () => (
            <div className="flex flex-col h-full bg-gray-50 shadow-lg ">
                  {/* Header Section */}
                  <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-gray-50 via-gray-100 to-white mt-2">
                        <div className="flex items-center justify-between bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-300">
                              {/* Business Name Section */}
                              <div className="flex items-center space-x-4 flex-1 min-w-0">
                                    <div className="h-10 w-10 rounded-full bg-primary flex items-center justify-center text-xl font-bold text-white shadow-inner ring-2 ring-offset-2 ring-primary/30 flex-shrink-0">
                                          {loaderData?.userDetails?.userDetails?.businessName?.[0]?.toUpperCase() || "B"}
                                    </div>
                                    <div className="text-gray-900 text-xl font-semibold truncate">
                                          {loaderData?.userDetails?.userDetails?.businessName || "Business Name"}
                                    </div>
                              </div>


                        </div>
                  </div>

                  {/* Navigation Section */}
                  <nav className="flex-1 p-4 space-y-2">
                        <div className="space-y-1">
                              <Link to="/sellerSetting/customerAnalysis" onClick={() => setIsOpen(false)}>
                                    <Button
                                          variant={activeSection === "customerAnalysis" ? "secondary" : "ghost"}
                                          className={`w-full justify-start text-gray-700 py-3 px-4 rounded-lg transition-all duration-200 ${activeSection === "customerAnalysis"
                                                ? "bg-indigo-100 text-indigo-800"
                                                : "hover:bg-gray-100 hover:text-indigo-600"
                                                }`}
                                    >
                                          <div className="flex items-center mr-3">
                                                <ChartNoAxesCombined className="text-primary text-xl" />
                                                <span className="ml-2 text-gray-800 font-medium">Customer Analysis</span>
                                          </div>
                                    </Button>
                              </Link>

                              <Link to="/sellerSetting/nbanners" onClick={() => setIsOpen(false)}>
                                    <Button
                                          variant={activeSection === "nbanners" ? "secondary" : "ghost"}
                                          className={`w-full justify-start text-gray-700 py-3 px-4 rounded-lg transition-all duration-200 ${activeSection === "nbanners"
                                                ? "bg-indigo-100 text-indigo-800"
                                                : "hover:bg-gray-100 hover:text-indigo-600"
                                                }`}
                                    >

                                          <div className="flex items-center mr-3">
                                                <GalleryHorizontal className="text-primary " size={20} />
                                                <span className="ml-2 text-gray-800 font-medium">Banners & Sequence</span>
                                          </div>
                                    </Button>
                              </Link>

                              <Link to="/sellerSetting/whatsappprofile" onClick={() => setIsOpen(false)}>
                                    <Button
                                          variant={activeSection === "whatsappprofile" ? "secondary" : "ghost"}
                                          className={`w-full justify-start text-gray-700 py-3 px-4 rounded-lg transition-all duration-200 ${activeSection === "whatsappprofile"
                                                ? "bg-indigo-100 text-indigo-800"
                                                : "hover:bg-gray-100 hover:text-indigo-600"
                                                }`}
                                    >
                                          <div className="flex items-center mr-3">
                                                <FaWhatsapp className="text-primary" size={20} /> {/* WhatsApp icon */}
                                                <span className="ml-2 text-gray-800 font-medium">WhatsApp Settings</span>
                                          </div>                                    </Button>
                              </Link>
                              <Link to="/sellerSetting/coupons" onClick={() => setIsOpen(false)}>
                                    <Button
                                          variant={activeSection === "coupon" ? "secondary" : "ghost"}
                                          className={`w-full justify-start text-gray-700 py-3 px-4 rounded-lg transition-all duration-200 ${activeSection === "coupons"
                                                ? "bg-indigo-100 text-indigo-800"
                                                : "hover:bg-gray-100 hover:text-indigo-600"
                                                }`}
                                    >
                                          <span className="mr-3">🎉</span> Coupons
                                    </Button>
                              </Link>
                        </div>
                  </nav>

                  {/* Optional Footer Section */}
                  <div className="p-4 border-t border-gray-200">
                        <p className="text-xs text-gray-500 text-center">
                              © {new Date().getFullYear()} mNET
                        </p>
                  </div>
            </div>
      );

      return (
            <div className="min-h-screen bg-white">
                  <Sheet open={isOpen} onOpenChange={setIsOpen}>
                        <SheetContent side="left" className="w-64 p-0">
                              <NavContent />
                        </SheetContent>
                        <div className="md:pl-64">
                              <main className="flex-1">
                                    {/* Pass setIsOpen to children through Outlet context */}
                                    <Outlet context={{ setIsOpen }} />
                              </main>
                        </div>
                  </Sheet>
            </div>
      );
}
