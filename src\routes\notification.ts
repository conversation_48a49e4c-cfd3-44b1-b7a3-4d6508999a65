import { Router } from 'express';
import NotificationController from '@controllers/notification.controller.js';
import { Routes } from '@interfaces/routes.interface.js';
import {authenticateToken} from "@middleware/auth.js"
 

class AuthRoute implements Routes {
  public path = '/api/v1/notification';
  public router = Router();
  public notificationController = new NotificationController();

  constructor() {
    this.initializeRoutes();
  }

  private initializeRoutes() {
    this.router.post(`${this.path}/sendNotification`,authenticateToken, this.notificationController.sendNotification);
    this.router.post(`${this.path}/register-user`, authenticateToken,this.notificationController.registerUser);
  }
}

export default AuthRoute;
